{"7c50481bc992b9ff109abbeeeece073a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "949ca3c8b711de72", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach']，实际响应: '['check mobile data balance of sim2', 'SIM 2 not detected. Please check and try again.', '', '', '', '']'\nassert False", "time": {"start": 1755710879509, "stop": 1755710900214, "duration": 20705}}]}, "8a87a1d0f534afc4770901f3d1dfa316": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "493aee631e1e1647", "status": "passed", "time": {"start": 1755709041383, "stop": 1755709061912, "duration": 20529}}]}, "1f9da5f1f2977bbbae31e0d3334eff82": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f64c738cc7a9bff6", "status": "passed", "time": {"start": 1755709428263, "stop": 1755709448886, "duration": 20623}}]}, "4dcdc98a8f38f748728aec71f673e025": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "516f66c98dd46d2", "status": "passed", "time": {"start": 1755705058382, "stop": 1755705174540, "duration": 116158}}]}, "c50847e2010bac3c5a9bb7ff0b690fb6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "605839e96459bcfc", "status": "passed", "time": {"start": 1755715655727, "stop": 1755715676438, "duration": 20711}}]}, "8e367b8da758818b9a0fe21deca7ec48": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c6b5a4591b5250a3", "status": "passed", "time": {"start": 1755716720450, "stop": 1755716740617, "duration": 20167}}]}, "783144ed3c9603f07a5306d78cb4fde3": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d0a6623db05a7ecf", "status": "skipped", "statusDetails": "Skipped: power off 会导致设备断开，先跳过", "time": {"start": 1755707014398, "stop": 1755707014398, "duration": 0}}]}, "70c9bd8c4aab57e96eb06acb93ca2223": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "33c23c78138d1c18", "status": "passed", "time": {"start": 1755713225256, "stop": 1755713248389, "duration": 23133}}]}, "57c053de6acd628d4b4cd1230b702a40": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "66f53df8d11303cb", "status": "passed", "time": {"start": 1755711161634, "stop": 1755711181723, "duration": 20089}}]}, "1a5cbbb97cbe59e003ae71750a8d910f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c33639a362546746", "status": "passed", "time": {"start": 1755704619903, "stop": 1755704641600, "duration": 21697}}]}, "1d8131ec3deb65d953f2f16c259f261b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5a2a7f94ed174bd7", "status": "passed", "time": {"start": 1755703043747, "stop": 1755703065191, "duration": 21444}}]}, "36066a5bbb1f962a6ad5842baefe4ff3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ade945068fe9004d", "status": "passed", "time": {"start": 1755714975801, "stop": 1755715002708, "duration": 26907}}]}, "de0ed312f350c708e7a00bb74aeaac0f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fc372c49af1edc60", "status": "passed", "time": {"start": 1755701539602, "stop": 1755701560876, "duration": 21274}}]}, "fc477656b55eea3a3906a5bdcaa93554": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e277df3e3e307782", "status": "passed", "time": {"start": 1755710056245, "stop": 1755710078932, "duration": 22687}}]}, "3eec462c8da39eaf95742ed9ab45b7c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "657ed2213e67f17f", "status": "passed", "time": {"start": 1755711490529, "stop": 1755711511192, "duration": 20663}}]}, "a2c60aff2518d86c9e5686c943130f40": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cead85b9790eb619", "status": "passed", "time": {"start": 1755717726216, "stop": 1755717746762, "duration": 20546}}]}, "f27eb1a59c1d5c5c845852e05e6a17d9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "857baf7b1c9bdf69", "status": "passed", "time": {"start": 1755706735522, "stop": 1755706757517, "duration": 21995}}]}, "d45827de1782723ad9f8cd9d38f067dc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4de5ac35d0b605d7", "status": "passed", "time": {"start": 1755715911170, "stop": 1755715941571, "duration": 30401}}]}, "9b6faa79e3fe09fed639b1092082745b": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4105641adf8eeed4", "status": "skipped", "statusDetails": "Skipped: 语言设置为中文，影响别的Case，先跳过", "time": {"start": 1755705767182, "stop": 1755705767182, "duration": 0}}]}, "c5debd3414c81b3cc4349236d4020867": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "878d84142f96b7be", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['your Infinix phone assistant', 'your TECNO phone assistant']，实际响应: '['i am your voice assistant', '', '', '', \"That's right! I am <PERSON>, your autonomous phone assistant, developed by TECNO. How can I assist you today?\", 'Generated by AI, for reference only', 'Dialogue', \"02:09 Dialogue Explore Swipe down to view earlier chats Starlink U.S. Outage Again Four Liverpool Stars in PFA Team How to use Ask About Screen i am your voice assistant That's right! I am <PERSON>, your autonomous phone assistant, developed by TECNO. How can I assist you today? Generated by AI, for reference only What are your features? Can you tell me more about TECNO? I am glad to know you. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "time": {"start": 1755713371662, "stop": 1755713392497, "duration": 20835}}]}, "19cc9ff22947964a912a8f57a87a3c68": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ed3602d899fe4932", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['could you please search an for me', '', '', '', \"I can't search for you right now, but I can help with other things! Is there anything else you need?\", 'Generated by AI, for reference only', 'Dialogue', \"23:02 Dialogue Explore <PERSON>wipe down to view earlier chats OpenAI Unveils GPT-5, GPT-6 Future Gutfeld: <PERSON>'s Plain-Speak Appeal How to use Ask About Screen could you please search an for me I can't search for you right now, but I can help with other things! Is there anything else you need? Generated by AI, for reference only What can you search for? Can you search for images? I need other help. DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "time": {"start": 1755702099861, "stop": 1755702121075, "duration": 21214}}]}, "da0740a44317121ce1879d022e95ae23": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "70e614510c75c93b", "status": "passed", "time": {"start": 1755704737411, "stop": 1755704814836, "duration": 77425}}]}, "1bf9bd9c91ab7da6f818ff587cfff7da": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "24e9abe1ba38f937", "status": "passed", "time": {"start": 1755703430671, "stop": 1755703454457, "duration": 23786}}]}, "b68db9e7b007fe641926babf537afa6c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "bf0190e9cd5a8476", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Brightness goes down']\nassert False", "time": {"start": 1755706019510, "stop": 1755706040717, "duration": 21207}}]}, "e8f9ac327bc5f166a4c4a14509f365bf": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2db03739558319f1", "status": "passed", "time": {"start": 1755708760066, "stop": 1755708780737, "duration": 20671}}]}, "3bd2ea64304a19b2c3694e3a1975c668": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ef8f26c2041d11be", "status": "passed", "time": {"start": 1755712909088, "stop": 1755712930580, "duration": 21492}}]}, "6d49aaf4a5e11b32b961aa0aa3dbbf6e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2e6315addcbe0091", "status": "passed", "time": {"start": 1755714328537, "stop": 1755714351030, "duration": 22493}}]}, "ac6792be4ebb553a5655a2c44aca218c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5fb1f8cdacb21403", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['the high is forecast', '℃']\nassert False", "time": {"start": 1755704350521, "stop": 1755704378244, "duration": 27723}}]}, "6c59bb8b6ba250c58da738ab8237ed3c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e121af0e43898b5", "status": "passed", "time": {"start": 1755703271698, "stop": 1755703294462, "duration": 22764}}]}, "13916c423237bf06a74b0b109aad0d66": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "46bca85c836641b0", "status": "passed", "time": {"start": 1755707350893, "stop": 1755707372785, "duration": 21892}}]}, "b236a18e19a6aa2218c85b80afef2746": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7e0add9ec94ae39b", "status": "passed", "time": {"start": 1755703008961, "stop": 1755703029423, "duration": 20462}}]}, "7ec382c77bede0ad015817770cd9e1eb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "45f1052203b29d68", "status": "passed", "time": {"start": 1755702937954, "stop": 1755702959235, "duration": 21281}}]}, "5dff8ffa0041df33df80919398086e48": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3c2e0c6fd2ccf328", "status": "passed", "time": {"start": 1755708162654, "stop": 1755708183285, "duration": 20631}}]}, "f622c7c4831272dc58cb99e6af8d9943": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "938d0ec59845c0f0", "status": "passed", "time": {"start": 1755709735934, "stop": 1755709764303, "duration": 28369}}]}, "5fc780d1e7f790011f0e4a521e125a16": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2b7fde4df860431e", "status": "passed", "time": {"start": 1755717005889, "stop": 1755717026585, "duration": 20696}}]}, "e2beda2a0bda4155b33d47f14bdcb9ed": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ebb316f3e15e7982", "status": "passed", "time": {"start": 1755715956427, "stop": 1755715977020, "duration": 20593}}]}, "468e62202269d66d1ea3c0ae6e2a0e21": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2fb30ed5d8c97efd", "status": "passed", "time": {"start": 1755710415835, "stop": 1755710436723, "duration": 20888}}]}, "2b244b852ff1236f560ec792596ae556": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "93fccfa236b74211", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Sorry']\nassert False", "time": {"start": 1755714869937, "stop": 1755714890353, "duration": 20416}}]}, "9e84af065ec0018044fd43f37b4c2179": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3b0f87ed2ba47792", "status": "passed", "time": {"start": 1755712944825, "stop": 1755712965147, "duration": 20322}}]}, "148d3ba280bfe2b41b8464beec5f6763": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b735d7161e2ebf8b", "status": "passed", "time": {"start": 1755701283744, "stop": 1755701319743, "duration": 35999}}]}, "de524bccf252aabc016822f1a65de7f4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "68ed7aab547d2d9d", "status": "passed", "time": {"start": 1755702973812, "stop": 1755702994422, "duration": 20610}}]}, "fe7a0f349fbd2027990e72ab5a6650f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "64169ad1b4b90399", "status": "passed", "time": {"start": 1755709355890, "stop": 1755709376884, "duration": 20994}}]}, "0fa1017773031b1388876c73f0e0e653": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dc18d3af30b26f69", "status": "passed", "time": {"start": 1755709319712, "stop": 1755709340963, "duration": 21251}}]}, "04cef4934fe29e90ca0248af7b395794": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5ddbc58a02a7ab5c", "status": "passed", "time": {"start": 1755707859515, "stop": 1755707880347, "duration": 20832}}]}, "fe3d09fe0bad56e7804ef2f5ea49d283": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "84bf7ebf445383bc", "status": "passed", "time": {"start": 1755706158440, "stop": 1755706182603, "duration": 24163}}]}, "acca7d0b06a28ad8e6ccfe3c35828ce1": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5a47ad7486e4ec29", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['is', 'The high is forecast as', 'and the low as', '℃']\nassert False", "time": {"start": 1755704541401, "stop": 1755704569475, "duration": 28074}}]}, "223077c4b7372197db11e72856c8b7e6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "827684d8379c54c4", "status": "passed", "time": {"start": 1755714127374, "stop": 1755714150399, "duration": 23025}}]}, "6bdbaaabff6497c5d3be4727f1a7cd8d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9abdc970d1b20950", "status": "passed", "time": {"start": 1755701612443, "stop": 1755701635846, "duration": 23403}}]}, "693bce6b8bfdefa98827246122355bf1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d683cbee37868329", "status": "passed", "time": {"start": 1755712406479, "stop": 1755712426861, "duration": 20382}}]}, "762b1ab748e39965c1484eb7fe38bfe4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e218cc5b5c759e28", "status": "passed", "time": {"start": 1755701110195, "stop": 1755701131153, "duration": 20958}}]}, "d6d97ebce763bf8ead601650bfb2383c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8a42e411053541d3", "status": "passed", "time": {"start": 1755702135941, "stop": 1755702156660, "duration": 20719}}]}, "2b4b7555520a6b757239820871731d81": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3f9822a9ba521174", "status": "passed", "time": {"start": 1755714657246, "stop": 1755714690988, "duration": 33742}}]}, "4aad505422b0d8c87c499477cd89ff5d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "99ec4f33dc304231", "status": "passed", "time": {"start": 1755706055513, "stop": 1755706078043, "duration": 22530}}]}, "e8c3c7bb72cf538a9e89a7b790c5e689": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "52b1168d520f1090", "status": "passed", "time": {"start": 1755717509854, "stop": 1755717530141, "duration": 20287}}]}, "6c46a38570672e3c21f37ef82690d639": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ce467c19240b201f", "status": "passed", "time": {"start": 1755708330448, "stop": 1755708372133, "duration": 41685}}]}, "79b68fd9ac84793c5f55250aad03649a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b9a2b43807e7424c", "status": "passed", "time": {"start": 1755707930297, "stop": 1755707950934, "duration": 20637}}]}, "ad18e983dce31052b87b7404f3b347ce": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "339ac4fc9f765983", "status": "passed", "time": {"start": 1755705598498, "stop": 1755705619924, "duration": 21426}}]}, "05cfb3e6f186373be53bb1a7166ac69f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bbb773782dcfa0d8", "status": "passed", "time": {"start": 1755715242940, "stop": 1755715270305, "duration": 27365}}]}, "ff945a5d436679bddd13261b231955ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d6477c3a823b2561", "status": "passed", "time": {"start": 1755716241228, "stop": 1755716262243, "duration": 21015}}]}, "83e3a1b41834e87017b680efd7c16b92": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "acd10b2f8b72c4b2", "status": "passed", "time": {"start": 1755703803433, "stop": 1755703825132, "duration": 21699}}]}, "50e811b93ce50e5ac8364a9fea07e234": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c8e8c9cb0c056565", "status": "passed", "time": {"start": 1755713085071, "stop": 1755713105407, "duration": 20336}}]}, "32ba476d46e86e963aa12ced39981955": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "eb70cf2137fe243f", "status": "passed", "time": {"start": 1755705865868, "stop": 1755705886037, "duration": 20169}}]}, "b07852caec1a6673427b80552f64be85": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5e31517853f4fdb7", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording finished']\nassert False", "time": {"start": 1755706093068, "stop": 1755706142874, "duration": 49806}}]}, "af89bd1d18cd6a175678a8fe1f43ee33": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2010785e0c89ed86", "status": "passed", "time": {"start": 1755714741099, "stop": 1755714772241, "duration": 31142}}]}, "039e454ca4c329751543f1bfbb5e008e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e468d6a321e1e20", "status": "passed", "time": {"start": 1755709823196, "stop": 1755709851513, "duration": 28317}}]}, "f2f6762c5ec83e110ace25b47e3112d5": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d41160b7175fd6cf", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['℃']\nassert False", "time": {"start": 1755702473412, "stop": 1755702500832, "duration": 27420}}]}, "d8a3659601151a79f3b71ba4e47cafee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9e55628222e59320", "status": "passed", "time": {"start": 1755717901535, "stop": 1755717922341, "duration": 20806}}]}, "f9558282973df5c72bd1c57fb0e19984": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7a4f53635514e0ea", "status": "passed", "time": {"start": 1755710165942, "stop": 1755710186411, "duration": 20469}}]}, "eb142151b4b5ba4125a1a866dc2b58ef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a2ff278134401cef", "status": "passed", "time": {"start": 1755716613470, "stop": 1755716634162, "duration": 20692}}]}, "3238a485ed8e76dc2866c0b0bcd4930e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6b2b66f9a390a323", "status": "passed", "time": {"start": 1755707230071, "stop": 1755707251658, "duration": 21587}}]}, "ff8706df57207971727cf6e1326d4a26": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b9658b259c961db2", "status": "passed", "time": {"start": 1755714705539, "stop": 1755714726431, "duration": 20892}}]}, "95e68fb1d20b8d7ff190c67b0bbc2ee8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6268808e2335924f", "status": "passed", "time": {"start": 1755703945988, "stop": 1755703966624, "duration": 20636}}]}, "a19924fb0a564cf26596907610c0f678": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bab75f16bde0ccde", "status": "passed", "time": {"start": 1755702786033, "stop": 1755702808456, "duration": 22423}}]}, "503ff57584874e8387e6b367bfa70c8c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "69e17ba55154faec", "status": "passed", "time": {"start": 1755711525591, "stop": 1755711546116, "duration": 20525}}]}, "c190fe929896ea57ed1e33f8bc5bf113": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3725a642eca617b0", "status": "passed", "time": {"start": 1755708649746, "stop": 1755708672277, "duration": 22531}}]}, "db313838c77140c89e69785e101f25d7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "309142b0a1a05401", "status": "passed", "time": {"start": 1755714293227, "stop": 1755714313661, "duration": 20434}}]}, "fcfaafeb2b49ed6f468b8db263c64a18": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e20f716311878e59", "status": "passed", "time": {"start": 1755706273406, "stop": 1755706295159, "duration": 21753}}]}, "7af47ccbdaf69e5292e05041f822c0c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4ee5197beb31c8d2", "status": "passed", "time": {"start": 1755708614679, "stop": 1755708635097, "duration": 20418}}]}, "c7b8111fa78410a413dfc969cfe6f0e1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e8f1a16a860d194a", "status": "passed", "time": {"start": 1755714786798, "stop": 1755714818888, "duration": 32090}}]}, "70c4e0c16f5cb3629f87876768739a8d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5156b101e511d7c4", "status": "passed", "time": {"start": 1755710721885, "stop": 1755710742518, "duration": 20633}}]}, "9da64d3434f91a12d693ed9c71b62e87": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3c5b657eb5c56f22", "status": "passed", "time": {"start": 1755700917970, "stop": 1755700940067, "duration": 22097}}]}, "8b2d3084bb429ea5def5db416bbf10a7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cf5abb140a3b934c", "status": "passed", "time": {"start": 1755700300794, "stop": 1755700335456, "duration": 34662}}]}, "f4da532f5d62abff197a05947efc027a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f94209c151cc2013", "status": "passed", "time": {"start": 1755702703335, "stop": 1755702733979, "duration": 30644}}]}, "fcda536a017e05b2edb24a2e80ce1ec0": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "64e2ef5736676f9d", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['What do you want it to say?']\nassert False", "time": {"start": 1755717544335, "stop": 1755717569297, "duration": 24962}}]}, "d29b58e8e7ac85336b7dc255831fd5ba": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a0516cb668e579d5", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['not installed yet. Please download the app and try again.', 'I need to download boomplay for android to continue']，实际响应: '['play music by boomplay', 'I need to download Boomplay to continue.', '', '', '', '']'\nassert False", "time": {"start": 1755703187627, "stop": 1755703210256, "duration": 22629}}]}, "bd4f9d0c0f70cf6b24bb9923810b25c1": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "621037e1a08d5de7", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Please state your parking address?']，实际响应: '['remember the parking space', 'You can tap More to modify the recorded word information, or use the camera and function to record more information.', '', '', '', '']'\nassert False", "time": {"start": 1755715510818, "stop": 1755715531762, "duration": 20944}}]}, "733cc57b9e666f7c16017a85f41c410d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2641a32928086e2f", "status": "passed", "time": {"start": 1755706881299, "stop": 1755706903925, "duration": 22626}}]}, "f1bf796cd6804ca9b19a3e3f949a04ba": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2c089ad2a03c802d", "status": "passed", "time": {"start": 1755713755631, "stop": 1755713776536, "duration": 20905}}]}, "00495562396e9306113e5f37378ac991": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "def9a07194c6a68d", "status": "passed", "time": {"start": 1755712581668, "stop": 1755712602289, "duration": 20621}}]}, "c359e36718cf3ac8fd335c333a6470cf": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3400b192c234228", "status": "passed", "time": {"start": 1755714365612, "stop": 1755714391956, "duration": 26344}}]}, "71c64c122f83e6fd138db517bbda4aef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6b2b3674508b58e", "status": "passed", "time": {"start": 1755717189600, "stop": 1755717210441, "duration": 20841}}]}, "49ae31ee7fe8baa7f1604fd83d56bb68": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "327dcb048cc0fe4", "status": "passed", "time": {"start": 1755710915104, "stop": 1755710935537, "duration": 20433}}]}, "0e5513d569c7270e4332e484218ae36b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ce3e4bfb4f1d431d", "status": "passed", "time": {"start": 1755711895033, "stop": 1755711916062, "duration": 21029}}]}, "fee3033814a8b17ff8c8abe6bbcdc839": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e2ca15307ddb2dc0", "status": "passed", "time": {"start": 1755703393400, "stop": 1755703416211, "duration": 22811}}]}, "c796c03cca51cea23bdc87f3f9d6fa95": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "54ac4d0e60138b59", "status": "passed", "time": {"start": 1755713720283, "stop": 1755713741306, "duration": 21023}}]}, "e14cd5a605f26d24de7f5f63d4667c68": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cc141b0344339aa1", "status": "passed", "time": {"start": 1755716347825, "stop": 1755716374010, "duration": 26185}}]}, "4b3ad3bdf0873599e48d8f20d70246c9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b732fc1153a563f6", "status": "passed", "time": {"start": 1755706491479, "stop": 1755706513206, "duration": 21727}}]}, "cb21afc40e4aec32b847936756c8ba6e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "fe7678a6c04e5be5", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Spotify is not installed yet. Please download the app and try again.']\nassert False", "time": {"start": 1755701246247, "stop": 1755701269209, "duration": 22962}}]}, "cf3df9e3e259f083301aa2ec640729fe": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "88e8a16f703da5cb", "status": "passed", "time": {"start": 1755700881604, "stop": 1755700903197, "duration": 21593}}]}, "4bda544c08fa4bc5494c7dda01d4cc77": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e0bcd67d09add88c", "status": "passed", "time": {"start": 1755717866874, "stop": 1755717887416, "duration": 20542}}]}, "dbd7a7f96e1740fa05f50ed6fa7becfb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1b08313dfe29e27", "status": "passed", "time": {"start": 1755717153273, "stop": 1755717174591, "duration": 21318}}]}, "eb6abc860fad339739076abacb13ac83": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "427c0e6c562a36a3", "status": "passed", "time": {"start": 1755712370859, "stop": 1755712392060, "duration": 21201}}]}, "217ee9f7b3be9f625903076716d45106": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e36f71212ac3b25e", "status": "passed", "time": {"start": 1755714940752, "stop": 1755714961138, "duration": 20386}}]}, "0a0a3640b2ba4adce516043bd9362070": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5c18fefd24f91a0d", "status": "passed", "time": {"start": 1755700582867, "stop": 1755700603776, "duration": 20909}}]}, "87f3dc53ab72c729262e053c16a3dbcb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "27f56deb688575b7", "status": "passed", "time": {"start": 1755700385328, "stop": 1755700406429, "duration": 21101}}]}, "540cff5d6d552c22ec37f66efd17315f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f7750a7f3a5ebc38", "status": "passed", "time": {"start": 1755713897398, "stop": 1755713926872, "duration": 29474}}]}, "d4ded95517fa8a5af49f09554cc49725": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "49c7a831e04edfb5", "status": "passed", "time": {"start": 1755711930713, "stop": 1755711951175, "duration": 20462}}]}, "b9fc05e613fdd4d145434e9cf6378c4b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e2e4fd5ef808353d", "status": "passed", "time": {"start": 1755704163804, "stop": 1755704184666, "duration": 20862}}]}, "6075008522e5d0ae1667c4ac4be759eb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f79d0f1fb4940a1c", "status": "passed", "time": {"start": 1755701334357, "stop": 1755701366862, "duration": 32505}}]}, "400a9b197316d3b1e59fe33ed78a836a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8e790f7f798cb38b", "status": "passed", "time": {"start": 1755710609027, "stop": 1755710636987, "duration": 27960}}]}, "3215de286c6ddd59d6e52a44f2a9967d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b46b15d39e0b3b36", "status": "passed", "time": {"start": 1755711860041, "stop": 1755711880454, "duration": 20413}}]}, "0e7fd56ff1d5c85e0ce1830d9899313d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "58903762c7912ce0", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['not installed yet. Please download the app and try again.', 'I need to download boomplay for android to continue']，实际响应: '['play music on boomplayer', 'I need to download Boomplay to continue.', '', '', '', '']'\nassert False", "time": {"start": 1755703309148, "stop": 1755703332570, "duration": 23422}}]}, "657acdf17dda1a11abf6946763f6ed52": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e08a284bddf2d94", "status": "passed", "time": {"start": 1755712122091, "stop": 1755712143242, "duration": 21151}}]}, "104cf8a7ef102b6850b6d14f4cb14052": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ccb377177100fa65", "status": "passed", "time": {"start": 1755716063161, "stop": 1755716083985, "duration": 20824}}]}, "a8ceb9cec2faa4662cde95140569091b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a258242e9c32e9fb", "status": "passed", "time": {"start": 1755715059150, "stop": 1755715097376, "duration": 38226}}]}, "ebd2c9b6cd7c07b69e348328a207e18b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "96fb5e3e2559766c", "status": "passed", "time": {"start": 1755708579016, "stop": 1755708599998, "duration": 20982}}]}, "1bc9389e45f0f75c30d3dfb39134948d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9c3a90b4e5bc21e1", "status": "passed", "time": {"start": 1755716791351, "stop": 1755716812407, "duration": 21056}}]}, "c9c4c38d0ca341040a41178716623909": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "97eb7e4115d75c48", "status": "passed", "time": {"start": 1755706419084, "stop": 1755706440513, "duration": 21429}}]}, "123d65cc114fff79e459e14acfbcd445": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "929d72935693324b", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['turn on adaptive brightness', 'Auto-brightness is turned on now.', 'Auto-brightness', '', '', '']'\nassert None", "time": {"start": 1755708795199, "stop": 1755708815144, "duration": 19945}}]}, "92c8c8e017b096314ffde2f610a6791e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d4b28db88d96b053", "status": "passed", "time": {"start": 1755707266109, "stop": 1755707298386, "duration": 32277}}]}, "c046a1c6e6cc8effa10641e329b1cfad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "630bd3a65e5c550f", "status": "passed", "time": {"start": 1755717041701, "stop": 1755717062984, "duration": 21283}}]}, "459c099a876d1129ddcb7cb28663b756": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2ef78000037a1acc", "status": "passed", "time": {"start": 1755706197680, "stop": 1755706221962, "duration": 24282}}]}, "6f2c4144233271771cdd01a5c48ea3ca": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a48f4d786e1c8577", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['No relevant apps found', 'Please download and try again']，实际响应: '['download basketball', \"Sorry, I don't know the answer, you can try searching on the Internet\", '', '', '', '']'\nassert False", "time": {"start": 1755709612923, "stop": 1755709634652, "duration": 21729}}]}, "70b49b2a6719030c7c51e642fdaec270": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9535fcce1508ad8", "status": "passed", "time": {"start": 1755703839947, "stop": 1755703860715, "duration": 20768}}]}, "4f84a6588e41dde581e4eef4fccd6344": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "82733299fa7cd70e", "status": "passed", "time": {"start": 1755709534776, "stop": 1755709560729, "duration": 25953}}]}, "d60fcab377d9b5093e0f03ecf20f5d10": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9fa336ffb418ce25", "status": "passed", "time": {"start": 1755702247237, "stop": 1755702269458, "duration": 22221}}]}, "6980dbcce9a72cd9dea6dee04c6891de": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "535c8ebe7b4c0300", "status": "passed", "time": {"start": 1755712839550, "stop": 1755712859910, "duration": 20360}}]}, "7acb737855a3a3110ed556a3e5fe1256": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f44afbc0139dc9a7", "status": "passed", "time": {"start": 1755707533841, "stop": 1755707583695, "duration": 49854}}]}, "99709ca7d9951f6f7049b49ea81d0cd1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "23f3c3c2daaeec8", "status": "passed", "time": {"start": 1755706991973, "stop": 1755707013080, "duration": 21107}}]}, "44720253edec52ccf0868b33f1938265": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cf0c2892ea9348fd", "status": "passed", "time": {"start": 1755717796370, "stop": 1755717817717, "duration": 21347}}]}, "ecfbca0f5d1122fac0e0543e38291ce2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d535b9830d965510", "status": "passed", "time": {"start": 1755707746600, "stop": 1755707773529, "duration": 26929}}]}, "b89775573784e6ef95769309baebeae4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cbd725d38ded5c9e", "status": "passed", "time": {"start": 1755709649631, "stop": 1755709675727, "duration": 26096}}]}, "876e77318cece5d1079b726f0c97bc45": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a70e7d90c6f3b074", "status": "passed", "time": {"start": 1755702025810, "stop": 1755702048461, "duration": 22651}}]}, "3333dd58fd9312a504ae6bc6edf830af": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "180455e93fc454c4", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['These suggestions are for your reference', 'the information I found for you']\nassert False", "time": {"start": 1755704699610, "stop": 1755704722347, "duration": 22737}}]}, "b5e1711cce3102fc710ff74e18bf9129": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "63f252b21b68ebf8", "status": "passed", "time": {"start": 1755717078070, "stop": 1755717098532, "duration": 20462}}]}, "19df0c79ab8c9ce909771e1a9d21fed3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ace97ecbc38601f0", "status": "passed", "time": {"start": 1755718050591, "stop": 1755718078306, "duration": 27715}}]}, "b6eee20d1fad16a048ce8490d7189be4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6f89a476c469d526", "status": "passed", "time": {"start": 1755710486077, "stop": 1755710506595, "duration": 20518}}]}, "7b90ee3ed7bdd2837a37215aac61cfd9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a6f80c7773f88ea5", "status": "passed", "time": {"start": 1755709391731, "stop": 1755709413674, "duration": 21943}}]}, "18377608d977aa5cb5e2fc6b03b9ad05": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "143173435c555345", "status": "passed", "time": {"start": 1755714090161, "stop": 1755714112687, "duration": 22526}}]}, "3a27fef360a79f638f96f0461df262da": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6f2361cbbbff8b05", "status": "passed", "time": {"start": 1755716205906, "stop": 1755716226591, "duration": 20685}}]}, "2428ad915810150c12838b88ee13f49c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "85e50e395982bf38", "status": "passed", "time": {"start": 1755702894282, "stop": 1755702923163, "duration": 28881}}]}, "b8a3cad490db8fccbe1d65628faf6743": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "150268bbf6efdd56", "status": "passed", "time": {"start": 1755714526972, "stop": 1755714551414, "duration": 24442}}]}, "563b9c2c8d2e1d68901eeac733e12913": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a0cc9190d48976f5", "status": "passed", "time": {"start": 1755706345675, "stop": 1755706366929, "duration": 21254}}]}, "fc75b92fb4a100575b2c948dd6c5a008": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "42fda6eca650eb29", "status": "passed", "time": {"start": 1755712194449, "stop": 1755712214581, "duration": 20132}}]}, "9511be8e6426d5078713c6e78f3b02e3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6c5f86b548af54ce", "status": "passed", "time": {"start": 1755706918545, "stop": 1755706939645, "duration": 21100}}]}, "ae86b8d534909e1e7c8c7adb4ee39e5c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "738b70d304f66c90", "status": "passed", "time": {"start": 1755709006278, "stop": 1755709026560, "duration": 20282}}]}, "fa47cb0b4427dd62fb8f91c9e5e15ace": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "be63ad83ca192ad0", "status": "passed", "time": {"start": 1755718284705, "stop": 1755718306600, "duration": 21895}}]}, "57acf2797af332487c1fdb9a53a30e4f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "795650584e329424", "status": "passed", "time": {"start": 1755711965465, "stop": 1755711985632, "duration": 20167}}]}, "a416a85ec867e3b2cfd6e23150d72859": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d8c8fcb4df01608b", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', 'reached the image generation limit for today']，实际响应: '['Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations', '', '', '', \"That sounds like a lovely idea! Designing a high-end jewelry ring themed around flamingo elements for e-commerce illustrations could be really stunning.\\n\\nHere are some ideas to consider for the design:\\n\\nShape and Silhouette: The ring could feature a flamingo silhouette, perhaps with the body of the flamingo forming the main part of the ring and the legs extending around the finger.\\nMaterials: Use high-quality materials like platinum or gold. Incorporate pink gemstones like pink sapphires, rubies, or tourmalines to represent the flamingo's feathers, and perhaps diamonds for added sparkle.\\nDetails: Consider adding intricate details like feathers carved into the metal or a delicate beak crafted with precision.\\nStyle: The ring could be modern, minimalist, or more ornate, depending on the target audience.\\n\\nSince you're interested in e-commerce illustrations, think about how the ring will be presented\", 'Generated by AI, for reference only', 'Dialogue', \"01:34 Dialogue Explore Swipe down to view earlier chats That sounds like a lovely idea! Designing a high-end jewelry ring themed around flamingo elements for e-commerce illustrations could be really stunning.&#10;&#10;Here are some ideas to consider for the design:&#10;&#10;Shape and Silhouette: The ring could feature a flamingo silhouette, perhaps with the body of the flamingo forming the main part of the ring and the legs extending around the finger.&#10;Materials: Use high-quality materials like platinum or gold. Incorporate pink gemstones like pink sapphires, rubies, or tourmalines to represent the flamingo's feathers, and perhaps diamonds for added sparkle.&#10;Details: Consider adding intricate details like feathers carved into the metal or a delicate beak crafted with precision.&#10;Style: The ring could be modern, minimalist, or more ornate, depending on the target audience.&#10;&#10;Since you're interested in e-commerce illustrations, think about how the ring will be presented. Consider different angles, lighting, and backgrounds to showcase the ring's beauty. Would you lik DeepSeek-R1 Feel free to ask me any questions…\"]'\nassert False", "time": {"start": 1755711231762, "stop": 1755711255108, "duration": 23346}}]}, "b7bfa1ba094155307273abc83c43a0d5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9b8438d7ed2a785e", "status": "passed", "time": {"start": 1755703114361, "stop": 1755703134975, "duration": 20614}}]}, "c0d6ce0b7c5e41242c01a6e0c0186608": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6992a6d49c5017f9", "status": "passed", "time": {"start": 1755704469574, "stop": 1755704490346, "duration": 20772}}]}, "643a7bbfbf5c5eedbae7ae814fbc8b52": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "73fe2b51030a055c", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The current charging device does not support switching charging modes']\nassert False", "time": {"start": 1755708197642, "stop": 1755708217597, "duration": 19955}}]}, "3dae350db69abded432b3e7f5f8463c8": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "595d2d67a593ff6f", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"Here's a joke for you\"]\nassert False", "time": {"start": 1755704128497, "stop": 1755704149109, "duration": 20612}}]}, "44c9275711c93730c8d2cb2a7374b3cd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f88d0ff2665d71d8", "status": "passed", "time": {"start": 1755711269779, "stop": 1755711290342, "duration": 20563}}]}, "772728b3468560788490a3673352724d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3602fab948853dc9", "status": "passed", "time": {"start": 1755704088367, "stop": 1755704113810, "duration": 25443}}]}, "92e2909ea81e82011e43342b4fc06c3b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3bf62dda522e8ea6", "status": "passed", "time": {"start": 1755716098434, "stop": 1755716119033, "duration": 20599}}]}, "4933b925ec694ecfcd17b3423ac28184": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "aa022482314d4b36", "status": "passed", "time": {"start": 1755709906929, "stop": 1755709935577, "duration": 28648}}]}, "3d685d9ca6a0d7795be3c96921595318": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7ebf02b247aaeca5", "status": "passed", "time": {"start": 1755711560532, "stop": 1755711581398, "duration": 20866}}]}, "28f9087c186df37701fba71f366c084e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "71f530986a1c345", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The alarm']\nassert False", "time": {"start": 1755709498977, "stop": 1755709519819, "duration": 20842}}]}, "bb51fd67dac102de95e755be72996bd1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b7ad49db78ad9e00", "status": "passed", "time": {"start": 1755711596134, "stop": 1755711618061, "duration": 21927}}]}, "7d8296483e3dfc0902b42ccfe6759e59": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "70049df9715fc24f", "status": "passed", "time": {"start": 1755716531741, "stop": 1755716560064, "duration": 28323}}]}, "9458f45d3c37d9141658da9964a470f5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "85b0b9f3b49e2a52", "status": "passed", "time": {"start": 1755715839632, "stop": 1755715860057, "duration": 20425}}]}, "51b4b46de04a8c1e37077a9f688cb490": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "816c6a8b9c388749", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['WhatsApp is not installed yet']\nassert False", "time": {"start": 1755714833356, "stop": 1755714855317, "duration": 21961}}]}, "286e9cba8578d73b1f445f9d6d3a7d2e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7abda32690218b48", "status": "passed", "time": {"start": 1755710343414, "stop": 1755710363990, "duration": 20576}}]}, "4fd50eb7a7e49fc09f612442a33e3010": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a33f0f1e44b65ad8", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['WhatsApp is not installed yet']\nassert False", "time": {"start": 1755710565547, "stop": 1755710594408, "duration": 28861}}]}, "3e1e4da6344de7cdf40fa1d59c43dcc3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c5d972e9ac233338", "status": "passed", "time": {"start": 1755703981243, "stop": 1755704001914, "duration": 20671}}]}, "59f89eb284ef9300c926c5e2f1d3fd26": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a9d7364ec53b988c", "status": "passed", "time": {"start": 1755707424478, "stop": 1755707446436, "duration": 21958}}]}, "02dda06829926b51f170419357629e86": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c4e2031bf63fbd80", "status": "passed", "time": {"start": 1755712652462, "stop": 1755712673122, "duration": 20660}}]}, "4072ba85d37e03a8ef0a5dd9d0741631": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "13888369295144ee", "status": "passed", "time": {"start": 1755706808156, "stop": 1755706829809, "duration": 21653}}]}, "18415b75388fbfdac9a7e4232373c000": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "68c83f240cb84ef1", "status": "passed", "time": {"start": 1755702359010, "stop": 1755702381566, "duration": 22556}}]}, "3bafa6d8eb5b49bc5b77f1784275285e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "25d621e86d10808b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording finished']\nassert False", "time": {"start": 1755707788346, "stop": 1755707809791, "duration": 21445}}]}, "51c103053dd0c5596d9f4d9f178c3d8d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "137d3ab7ca2b4635", "status": "passed", "time": {"start": 1755710378620, "stop": 1755710401319, "duration": 22699}}]}, "178c119ddfd51f19a22377df428e3fc1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "783d1b35174d91e4", "status": "passed", "time": {"start": 1755710686900, "stop": 1755710707420, "duration": 20520}}]}, "a0efcebc4cee6024e690bd290b4f3fbb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2e076a866aa19b91", "status": "passed", "time": {"start": 1755717474882, "stop": 1755717495286, "duration": 20404}}]}, "12eb3852c333145c5906579f2346c37a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3219738aacaddb47", "status": "passed", "time": {"start": 1755702320625, "stop": 1755702344247, "duration": 23622}}]}, "3d6cfc87445d1bd76dceee439d00b3d6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cee60a7f7bc35e19", "status": "passed", "time": {"start": 1755702668144, "stop": 1755702688954, "duration": 20810}}]}, "75c9edd252211f9e74fd8c1a2faeefd1": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ad3c5fc9da25816b", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording finished']\nassert False", "time": {"start": 1755707112931, "stop": 1755707133620, "duration": 20689}}]}, "1615e8617cafbed9e30baf38018d96b0": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "68c68f0ff335b08c", "status": "skipped", "statusDetails": "Skipped: reset phone 会导致设备断开，先跳过", "time": {"start": 1755715568953, "stop": 1755715568953, "duration": 0}}]}, "44e27936b56f219f63af671a8fd7f5fb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3eb7f8540804d8d9", "status": "passed", "time": {"start": 1755718138197, "stop": 1755718158627, "duration": 20430}}]}, "4ae696581fe41611547bc10ddba4f526": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "283291484f57bc21", "status": "passed", "time": {"start": 1755702748126, "stop": 1755702771669, "duration": 23543}}]}, "20bd2e7c8179ca1901bc63c9a02b9ee1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "959838ca0b62c989", "status": "passed", "time": {"start": 1755712762458, "stop": 1755712786139, "duration": 23681}}]}, "8bcc4c0c2b314e79a7177168f7d787b8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ff1097fbee878942", "status": "passed", "time": {"start": 1755710950108, "stop": 1755710970835, "duration": 20727}}]}, "5c75e7ecaa2fe55a9b9666aae0ca1b5a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "791c94de0efcf34", "status": "passed", "time": {"start": 1755717260698, "stop": 1755717286511, "duration": 25813}}]}, "c121335a9bce64dd570aaf5b221b21df": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f2c1e45e9870557e", "status": "passed", "time": {"start": 1755715546687, "stop": 1755715567418, "duration": 20731}}]}, "5697ea2b6f1cefef94d5b32e213e05a7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d353a8306eb76825", "status": "passed", "time": {"start": 1755705407528, "stop": 1755705518704, "duration": 111176}}]}, "56a09613cdb882018377e1c2c4e78472": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "51c00d45fa6af6db", "status": "passed", "time": {"start": 1755704584240, "stop": 1755704605401, "duration": 21161}}]}, "9445ebf64ec65c769332fec8dd505332": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3c088f68985d802", "status": "passed", "time": {"start": 1755717113523, "stop": 1755717138452, "duration": 24929}}]}, "bd4d204a449f3a4013b03af9a9101446": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "caa814f725fc2a01", "status": "passed", "time": {"start": 1755702515608, "stop": 1755702543010, "duration": 27402}}]}, "a306f4c2244f596f1ab838aa7a80dd45": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "32cde91effcb28af", "status": "passed", "time": {"start": 1755710272924, "stop": 1755710293558, "duration": 20634}}]}, "e8f03971277a71512b5ebaad612bc964": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "41a8d8e5a2de5632", "status": "passed", "time": {"start": 1755709779151, "stop": 1755709808495, "duration": 29344}}]}, "75c56947a7b1061f7ec858fb20919b50": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "eb3d8fcdee428cd0", "status": "passed", "time": {"start": 1755709865960, "stop": 1755709892497, "duration": 26537}}]}, "c45251d74b80bdc9105dde9b444ef1c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a155cce1034230a3", "status": "passed", "time": {"start": 1755711091508, "stop": 1755711112221, "duration": 20713}}]}, "88a6294df5f2ab484e181b1f196ff253": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6a9dd58982559231", "status": "passed", "time": {"start": 1755714012004, "stop": 1755714040036, "duration": 28032}}]}, "5f09c86ae320138eb73de73a25f73607": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4f7337a8b7498257", "status": "passed", "time": {"start": 1755713155584, "stop": 1755713175967, "duration": 20383}}]}, "0eb27e9cfa9ed24b7ee5e6be6e495cb7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e07ae7b684220b6a", "status": "passed", "time": {"start": 1755701427896, "stop": 1755701448480, "duration": 20584}}]}, "a4af452e0448ec3c1ecc9afcc30459be": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f84d3f6caf47f216", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The current charging device does not support switching charging modes']\nassert False", "time": {"start": 1755707824470, "stop": 1755707844851, "duration": 20381}}]}, "61e923bb9b35a687b231b0c27b5ec620": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "39a8d0fd53a2c6ce", "status": "passed", "time": {"start": 1755716970970, "stop": 1755716991444, "duration": 20474}}]}, "9c301cfc137fb94f119957b5f74291ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c48adfa7289fdf04", "status": "passed", "time": {"start": 1755718092438, "stop": 1755718123633, "duration": 31195}}]}, "929b39cceeb7a01f602573951f5fef39": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e9241c09bf9f876", "status": "passed", "time": {"start": 1755718249894, "stop": 1755718270338, "duration": 20444}}]}, "caccad19499f4bc8494953ac84d8d23c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f4c408a5aabd00f2", "status": "passed", "time": {"start": 1755704829532, "stop": 1755704941072, "duration": 111540}}]}, "e865942f74e70950eccebd8243dd6035": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "45fadb3dac70f843", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['I need to download yandex music, books & podcasts to continue.', 'not installed yet. Please download the app and try again.']，实际响应: '['video call mom through whatsapp', 'I need to download WhatsApp to continue.', '', '', '', '']'\nassert False", "time": {"start": 1755704199431, "stop": 1755704227984, "duration": 28553}}]}, "e78aa9affdc78502b8ad3b712ecf28b9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ac0cf8db9f9152f6", "status": "passed", "time": {"start": 1755706309995, "stop": 1755706331340, "duration": 21345}}]}, "776dca6a65836abb5a943543ee2b9f12": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "841dcfa4d7cc0b8a", "status": "passed", "time": {"start": 1755718428825, "stop": 1755718449822, "duration": 20997}}]}, "ffb0a39af30beaa699329479ec564117": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8d0be30952efafa5", "status": "passed", "time": {"start": 1755701145485, "stop": 1755701184603, "duration": 39118}}]}, "356db57bafcf61266d2f62fd1b8ab4e2": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "46eda86c2db0307b", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her', \"You've reached the image generation limit for today.\", '', '', '', '']'\nassert False", "time": {"start": 1755710307810, "stop": 1755710328714, "duration": 20904}}]}, "0c44c94f08feed70addcec44e96bda5a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d9e2a42bc660c8a2", "status": "passed", "time": {"start": 1755704315163, "stop": 1755704335969, "duration": 20806}}]}, "9c84b087eb7d9fde94ed5bb5370b275b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f0cdfab033dcf1e4", "status": "passed", "time": {"start": 1755714447563, "stop": 1755714473930, "duration": 26367}}]}, "ffd7dc86cbeda13ca78bbca09f06422a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "db51eea29e85759", "status": "failed", "statusDetails": "AssertionError: 初始=False, 最终=False, 响应='['countdown 5 min', 'Done!', '', '', '', '']'\nassert False", "time": {"start": 1755705975718, "stop": 1755706004279, "duration": 28561}}]}, "613ef0933e4be87696bbedd56b4f0052": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "86e30c2e6222b38", "status": "passed", "time": {"start": 1755710757053, "stop": 1755710777963, "duration": 20910}}]}, "17788b061637289a04732fe5840218ee": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ee35e6bf1fc33f7e", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['record audio for 5 seconds', 'Screen recording started.', '', '', '', '', '[com.transsion.screenrecorder页面内容] 开始录屏？ | 在录制内容时，Android 可以访问屏幕上显示或设备中播放的所有内容。因此，请务必小心操作，谨防密码、付款信息、消息、照片、音频和视频等内容遭到泄露。 | 麦克风 | 允许录屏时录制麦克风声音 | 显示点按效果']'\nassert False", "time": {"start": 1755701462783, "stop": 1755701489976, "duration": 27193}}]}, "1ca8d9c300d55584cdcf637ede08bdba": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "17d2a0a003d6d160", "status": "passed", "time": {"start": 1755711445164, "stop": 1755711475678, "duration": 30514}}]}, "1d15cba90ae0426fa12e3218f1c542a6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7487eccd0b883f61", "status": "passed", "time": {"start": 1755700618459, "stop": 1755700639682, "duration": 21223}}]}, "0b659537bc9c9b47c2c23f702fadd56b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b94f6474b6238d1a", "status": "passed", "time": {"start": 1755711632893, "stop": 1755711653385, "duration": 20492}}]}, "eadc304b3069d4918c06805d847a62d7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9b688eef9a94b838", "status": "passed", "time": {"start": 1755713120019, "stop": 1755713141026, "duration": 21007}}]}, "b7e448432379b6f8a430f1cbdb3ee3fb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4f291caf47ab1d3b", "status": "passed", "time": {"start": 1755703692385, "stop": 1755703716666, "duration": 24281}}]}, "1d703b69183fa98eda60c159840c4ffd": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5e14fb7d745f15a4", "status": "failed", "statusDetails": "AssertionError: contacts: 初始=False, 最终=False, 响应='['call mom', 'mom not found. Who would you like to call?', '', '', '', '']'\nassert False", "time": {"start": 1755710521217, "stop": 1755710551323, "duration": 30106}}]}, "0c4bd81bf0dbac094265e3ac47550bbd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ba45865cf853620b", "status": "passed", "time": {"start": 1755704278135, "stop": 1755704300338, "duration": 22203}}]}, "e8d2430f1712f99f0a178507bb398709": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "41c36b3af557b8cf", "status": "skipped", "statusDetails": "Skipped: 重启会导致设备断开，先跳过", "time": {"start": 1755715568963, "stop": 1755715568963, "duration": 0}}]}, "b4e75f584d82368436f820de28f92cfd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5b1d5fbfac869bd8", "status": "passed", "time": {"start": 1755701199251, "stop": 1755701232104, "duration": 32853}}]}, "753ba105235625e906d023bd3aaa0821": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b041df3626f271c3", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only', 'The current charging device does not support switching charging modes']，实际响应: '['Switch to Low-Temp Charge', 'Your phone has been fully charged.\\nIf you want to change the default charging mode, please say set default charging mode.', '', '', '', '']'\nassert False", "time": {"start": 1755708127204, "stop": 1755708147758, "duration": 20554}}]}, "b846e0492742bba04cf4a26ee4530889": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9c279d0adf8defad", "status": "skipped", "statusDetails": "Skipped: redial命令需要先拨打电话，才能重拨，无法通用化", "time": {"start": 1755715461202, "stop": 1755715461202, "duration": 0}}]}, "794f685415bbcd702feae0b55a4dd537": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cac94fb9e31f53e8", "status": "passed", "time": {"start": 1755703875261, "stop": 1755703896293, "duration": 21032}}]}, "ab2195315637668cad08b0606ef7ff17": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "89ffd0d94da459ee", "status": "passed", "time": {"start": 1755713791059, "stop": 1755713811626, "duration": 20567}}]}, "7b1fce8b7d3ff59dca969b439bb82f75": {"statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b0a48258d05596ff", "status": "broken", "statusDetails": "TypeError: a bytes-like object is required, not 'dict'", "time": {"start": 1755701835293, "stop": 1755701855687, "duration": 20394}}]}, "48a2a80bfed06f0c82b99a0aaa26e252": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9e5da72063ffe331", "status": "passed", "time": {"start": 1755713941081, "stop": 1755713962176, "duration": 21095}}]}, "f09a8375806e200073a99f1cbabdc35c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "119856ab8c08f6bd", "status": "passed", "time": {"start": 1755701074036, "stop": 1755701095411, "duration": 21375}}]}, "1ab60ce22774c460e557aaa3b3f9120a": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c41a5facdaf6e16", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording on hold']\nassert False", "time": {"start": 1755707639972, "stop": 1755707660359, "duration": 20387}}]}, "aecf9a6f67cd29766190cbcc133448d2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b76067150f1277eb", "status": "passed", "time": {"start": 1755715991624, "stop": 1755716012593, "duration": 20969}}]}, "ffd9b4e8a27f1469e05050bd5989e500": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c23bf9ba92d12972", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'The current charging device does not support switching charging modes']，实际响应: '['Switch to Hyper Charge', 'Your phone has been fully charged.\\nIf you want to change the default charging mode, please say set default charging mode.', '', '', '', '']'\nassert False", "time": {"start": 1755708092009, "stop": 1755708112333, "duration": 20324}}]}, "9420fd606a04614c6f09bf36f2873f93": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dd4f0642dceeb932", "status": "passed", "time": {"start": 1755707895286, "stop": 1755707915999, "duration": 20713}}]}, "1147a84f37b71eb5b15008169cadcc53": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9cda2cd83df5f4d2", "status": "passed", "time": {"start": 1755703768001, "stop": 1755703789007, "duration": 21006}}]}, "e9039096aff36ba6e4fae58e40eb8539": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7a5fabed6ebd69f9", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['not installed yet. Please download the app and try again.', 'I need to download Audiomack for android to continue']，实际响应: '['play music by Audiomack', 'I need to download audiomack to continue.', '', '', '', '']'\nassert False", "time": {"start": 1755715111807, "stop": 1755715134662, "duration": 22855}}]}, "57c5370b1a13de534ed16f0ce2ee85b9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c18c29d4fbf8459a", "status": "passed", "time": {"start": 1755708433297, "stop": 1755708454918, "duration": 21621}}]}, "c052c8813edd9c2261dc1bcc29786fe9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2e55777fd13a57c0", "status": "passed", "time": {"start": 1755717300879, "stop": 1755717321081, "duration": 20202}}]}, "e56b7788214bc4e18231f16dfd713954": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4c061d340532979d", "status": "passed", "time": {"start": 1755701650372, "stop": 1755701670821, "duration": 20449}}]}, "bfddb3863bb9971cace5dae92df6977d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1a010c5ee19b6419", "status": "passed", "time": {"start": 1755713299846, "stop": 1755713321399, "duration": 21553}}]}, "154a720f41d8f5a908552e8c7cf8e781": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ce3a29aa80ff9d13", "status": "passed", "time": {"start": 1755716027137, "stop": 1755716048487, "duration": 21350}}]}, "afa6af304cfb25a990764680de5fa777": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fa0470f75fef5cb4", "status": "passed", "time": {"start": 1755712229032, "stop": 1755712249507, "duration": 20475}}]}, "fcabf101e08450542157f8740eeec9a7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "26d776b4fe794136", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['whatsapp messenger is not installed yet', 'Please download the app and try again']\nassert False", "time": {"start": 1755709949839, "stop": 1755709971745, "duration": 21906}}]}, "d094a0b21c0bd532e6db707dcbab5564": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cd18059c198a0709", "status": "passed", "time": {"start": 1755711703536, "stop": 1755711723927, "duration": 20391}}]}, "f416bca94fc67372d77ac2dd1f3e4517": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4f4c41d20a93d13f", "status": "passed", "time": {"start": 1755703540319, "stop": 1755703563881, "duration": 23562}}]}, "5050d8dc816d181ca0c76dc56c8cb5f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "554df24716ad09c5", "status": "passed", "time": {"start": 1755713572625, "stop": 1755713593792, "duration": 21167}}]}, "4276e587385154206726240ad06acd24": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b51ab84faafc6791", "status": "passed", "time": {"start": 1755713861870, "stop": 1755713882539, "duration": 20669}}]}, "6ecc7e0fc961d0d4e7e46672c033625a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "137e5c8f5f4436f9", "status": "passed", "time": {"start": 1755702630224, "stop": 1755702653824, "duration": 23600}}]}, "8acd3c85f9c9d0b7f252da4466c049e6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ca8badb071247c64", "status": "passed", "time": {"start": 1755707027099, "stop": 1755707055915, "duration": 28816}}]}, "00ae864f57f2146374ff8bf301b9b8af": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8c91527bbabd6716", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.', '', '', '', \"Okay, I can help you visualize a hamster mascot with the characteristics you described! It sounds like you're aiming for a friendly and versatile character for commercial use.\\n\\nHere's a breakdown of how I imagine the hamster mascot, keeping your specifications in mind:\\n\\nOverall Look: A round and cute hamster with simple, clean lines. The style will be a solid line illustration with solid color fills, making it easily recognizable and adaptable.\\nExpressions: The mascot will have a range of expressions: happy, sad, and angry. These different postures will be key for conveying different emotions in your advertising.\\nColor Palette: Use bright, vibrant colors to create a lively and approachable feel. The color choices should be friendly and inviting.\\nAtmosphere: Soft lighting and a cozy atmosphere will enhance the mascot's\", 'Generated by AI, for reference only', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats Okay, I can help you visualize a hamster mascot with the characteristics you described! It sounds like you're aiming for a friendly and versatile character for commercial use.&#10;&#10;Here's a breakdown of how I imagine the hamster mascot, keeping your specifications in mind:&#10;&#10;Overall Look: A round and cute hamster with simple, clean lines. The style will be a solid line illustration with solid color fills, making it easily recognizable and adaptable.&#10;Expressions: The mascot will have a range of expressions: happy, sad, and angry. These different postures will be key for conveying different emotions in your advertising.&#10;Color Palette: Use bright, vibrant colors to create a lively and approachable feel. The color choices should be friendly and inviting.&#10;Atmosphere: Soft lighting and a cozy atmosphere will enhance the mascot's approachability.&#10;Dynamic Capture: The illustrations should capture movement and energy Generated by AI, for reference only DeepSeek-R1 Feel free to ask me any questions… 01:59\"]'\nassert False", "time": {"start": 1755712726345, "stop": 1755712747925, "duration": 21580}}]}, "b59687eed5ccb758650c7c0d96ed6bc1": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7b245ac8e527449f", "status": "skipped", "statusDetails": "Skipped: start walking命令都会开Health应用，才能执行，无法通用化", "time": {"start": 1755717287853, "stop": 1755717287853, "duration": 0}}]}, "a297156fb1e7af53c032ac3c6277feee": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "4e7f873cdb9a1a05", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The high is forecast as', '℃']\nassert False", "time": {"start": 1755718173048, "stop": 1755718199761, "duration": 26713}}]}, "7a670647c2336e6a5a5d07824fe89da6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7899813e415fcd74", "status": "passed", "time": {"start": 1755712263867, "stop": 1755712285139, "duration": 21272}}]}, "ce2018f4cca8041a465d2a753ade920b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a96fd6fef86d423", "status": "passed", "time": {"start": 1755705634423, "stop": 1755705655282, "duration": 20859}}]}, "098126ed77f375b3e0f5370b3ec7d0b7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d5ec8af845715ba7", "status": "passed", "time": {"start": 1755701685197, "stop": 1755701705762, "duration": 20565}}]}, "76a63bd8a63882a3a1ada32e63f1c971": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "886ff3ab35ea4e2d", "status": "passed", "time": {"start": 1755713684217, "stop": 1755713705584, "duration": 21367}}]}, "a0ea006ce61aacded2720f8d2a03ba5b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "18015d1175ce4542", "status": "passed", "time": {"start": 1755702823288, "stop": 1755702844554, "duration": 21266}}]}, "6748f677dff755a4da95c520c3f05506": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a3530bbd2b584699", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['These suggestions']\nassert False", "time": {"start": 1755701953332, "stop": 1755701973811, "duration": 20479}}]}, "8971377f4371d1ea3384cde4ed276db1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "847ab3cbd7b2ed1e", "status": "passed", "time": {"start": 1755708900298, "stop": 1755708921057, "duration": 20759}}]}, "0ce2a3efa79db58c34a5590015948f51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "df6d3dd2ff5385ab", "status": "passed", "time": {"start": 1755712800643, "stop": 1755712824978, "duration": 24335}}]}, "e76af38ac3a594aa2b7d7173d57e98ad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "903782cc98f3e172", "status": "passed", "time": {"start": 1755716863186, "stop": 1755716884039, "duration": 20853}}]}, "7cd08c87d5de8ec73ac863e8a636c8aa": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7c7a84e12488462f", "status": "passed", "time": {"start": 1755706844303, "stop": 1755706866562, "duration": 22259}}]}, "11875095b9997cfc7edbe407c3074b7e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "34e141b2776ec6e3", "status": "passed", "time": {"start": 1755711020853, "stop": 1755711041522, "duration": 20669}}]}, "d6ad2be1232377f5942b2d4f816b2e71": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a1f6a422ff2a1c03", "status": "passed", "time": {"start": 1755709076870, "stop": 1755709099324, "duration": 22454}}]}, "063471c2e7f2d00ecd08e780860e0cf2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c2f61d128b461d27", "status": "passed", "time": {"start": 1755708232186, "stop": 1755708253599, "duration": 21413}}]}, "464c8ea2a15f7ff86b8e0a347a821945": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "79a7c43986e8c28a", "status": "passed", "time": {"start": 1755701037928, "stop": 1755701059246, "duration": 21318}}]}, "94acf463e3e6d87a1e9cf7ff754044a2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f1b94fecde31a774", "status": "passed", "time": {"start": 1755714488350, "stop": 1755714512247, "duration": 23897}}]}, "d304f4e805a15a0351109cc931c26ffc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1dc62f6cb6eaeb3a", "status": "passed", "time": {"start": 1755703469271, "stop": 1755703489648, "duration": 20377}}]}, "f4d12b1367b35df96178a58e48fe8f5e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1bfcffb98f4a4154", "status": "passed", "time": {"start": 1755700542223, "stop": 1755700568328, "duration": 26105}}]}, "dee08db8cb0f1293bf864f56326992d5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "83e930097c463f5", "status": "passed", "time": {"start": 1755702209601, "stop": 1755702232800, "duration": 23199}}]}, "34f3c9cc9098f792051e7099b7a9fdc1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9d3cea02739dbf7e", "status": "passed", "time": {"start": 1755717439710, "stop": 1755717460594, "duration": 20884}}]}, "578e52c6d5e868d5464682b454971c51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ba657cd04a6dab4c", "status": "passed", "time": {"start": 1755709575847, "stop": 1755709598382, "duration": 22535}}]}, "306cbf11cdbcb045eb3c3c716515b1d6": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "29d552d1101b873b", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['tank you', 'Generated by AI, for reference only']，实际响应: '['', '', '', '', \"<Subject: Thank you>  \\n    \\n  Dear [Recipient's Name],  \\n    \\n  I hope this email finds you well. I am writing to express my sincere gratitude for [Specifically mention what you are thankful for.  Be detailed and genuine. E.g., your help with the project, your insightful feedback on my presentation, your hospitality during my visit, etc.].  Your [Mention specific action or quality, e.g.,  assistance, expertise, generosity] was invaluable and greatly appreciated.  \\n    \\n  I especially appreciate [Mention a specific detail you appreciated. E.g., your willingness to go the extra mile, your patience in explaining the complex concepts, your quick response to my request, etc.]. Your support has made a significant difference in [Explain how their action helped you. E.g.,  the success of the project, my understanding of the subject matter, my ability to overcome a challenge, etc.].  \\n    \\n  Thank you again for your kindness and support.  \\n    \\n  Best Regards,  \\n  [Your Name]  \\n  [Your Phone Number]  \\n  [Your Email Address]\", '', 'Dialogue', \"Dialogue Explore Swipe down to view earlier chats <Subject: Thank you>  &#10;    &#10;  Dear [Recipient's Name],  &#10;    &#10;  I hope this email finds you well. I am writing to express my sincere gratitude for [Specifically mention what you are thankful for.  Be detailed and genuine. E.g., your help with the project, your insightful feedback on my presentation, your hospitality during my visit, etc.].  Your [Mention specific action or quality, e.g.,  assistance, expertise, generosity] was invaluable and greatly appreciated.  &#10;    &#10;  I especially appreciate [Mention a specific detail you appreciated. E.g., your willingness to go the extra mile, your patience in explaining the complex concepts, your quick response to my request, etc.]. Your support has made a significant difference in [Explain how their action helped you. E.g.,  the success of the project, my understanding of the subject matter, my ability to overcome a challenge, etc.].  &#10;    &#10;  Thank you again for your kindness and support.  &#10;    &#10;  Best Regards,  &#10;  [Your Name]  &#10;  [Your Phone Number]  &#10;  [Your Email Address]  &#10; DeepSeek-R1 Feel free to ask me any questions… 02:08\"]'\nassert False", "time": {"start": 1755713262846, "stop": 1755713285052, "duration": 22206}}]}, "ed94d8d97b63a623a1f6438b57774ff1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6a6cc1e7cd8f4a82", "status": "passed", "time": {"start": 1755715017520, "stop": 1755715044358, "duration": 26838}}]}, "c5050ea089fe0f7a5b962119cd32b32e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "89017fbed8e16c62", "status": "passed", "time": {"start": 1755701800427, "stop": 1755701820824, "duration": 20397}}]}, "f5346ff0fa4cb76e4b6ceea6116693ee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6df2e183af43c36b", "status": "passed", "time": {"start": 1755703079746, "stop": 1755703100097, "duration": 20351}}]}, "8f69a86b2d665eb6925fa007d973040e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fbbff9436bd5a1d0", "status": "passed", "time": {"start": 1755706455372, "stop": 1755706476586, "duration": 21214}}]}, "5fe7611f5b7d3ef438ce938b66e0b99f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c5528074071c3cf3", "status": "passed", "time": {"start": 1755707598522, "stop": 1755707625184, "duration": 26662}}]}, "5ad5ef8bf0f7913e709dbc1e706db1e5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "710dc5b63057da8e", "status": "passed", "time": {"start": 1755717619004, "stop": 1755717639353, "duration": 20349}}]}, "79ed3b173757e627534e24ad9289f338": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1142c6b537107ee2", "status": "passed", "time": {"start": 1755718394394, "stop": 1755718414764, "duration": 20370}}]}, "8d12bedb52d3f000f4269afc25f3fe30": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "2ef45ceda14a7624", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['is', 'The high is forecast as', 'and the low as', '℃']\nassert False", "time": {"start": 1755704393144, "stop": 1755704419726, "duration": 26582}}]}, "2bf170e8c0013ab361afb23f8f059db8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "80a57a7e444b9600", "status": "passed", "time": {"start": 1755712036037, "stop": 1755712065049, "duration": 29012}}]}, "b911308f3c1fe764715d778a884946c2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d26e034db66f3bb1", "status": "passed", "time": {"start": 1755718464076, "stop": 1755718484775, "duration": 20699}}]}, "2ecac23eb3f511651fafc6ba6a3725f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fbcb39b15b93ef8d", "status": "passed", "time": {"start": 1755708391096, "stop": 1755708418442, "duration": 27346}}]}, "29293108cad153db021139a26e3455ee": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a1f27db92dd979ce", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Generated by AI, for reference only']\nassert False", "time": {"start": 1755717583953, "stop": 1755717604579, "duration": 20626}}]}, "5cc849d46714fff99c626e94dc28932d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f2bfad1ddd35325d", "status": "passed", "time": {"start": 1755710130878, "stop": 1755710151458, "duration": 20580}}]}, "d9e62135fb3d98a8cadd206c651db3d7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2d1447d297a62942", "status": "passed", "time": {"start": 1755707387423, "stop": 1755707409716, "duration": 22293}}]}, "a1088ee9683cc60d86b0994865138921": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7f92b55039421904", "status": "passed", "time": {"start": 1755703617920, "stop": 1755703643356, "duration": 25436}}]}, "a54454fcb441a45b0e29dcbbf21679aa": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2847dfe35b9b9f1a", "status": "passed", "time": {"start": 1755706563961, "stop": 1755706585204, "duration": 21243}}]}, "9ba28642fd60826e21a949609570a951": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fd391ebf1c24fdec", "status": "passed", "time": {"start": 1755715732878, "stop": 1755715753853, "duration": 20975}}]}, "5dbd6c476e40c9de0215f0509dd43986": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "751695afb4b430bd", "status": "passed", "time": {"start": 1755713536544, "stop": 1755713557931, "duration": 21387}}]}, "aff947fee562ec2636c3ce68a270b88d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "18890841c4c2c4ce", "status": "passed", "time": {"start": 1755708864308, "stop": 1755708885607, "duration": 21299}}]}, "411d5cfcc1960041b8df4decf67232f6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a415f76a255b6a92", "status": "passed", "time": {"start": 1755713050221, "stop": 1755713070693, "duration": 20472}}]}, "fef04fbdd26caf7d3f0f60df2c3ed14d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "63e40151836706cb", "status": "passed", "time": {"start": 1755712874437, "stop": 1755712894647, "duration": 20210}}]}, "7ebb09688c661659cc2b4a26d54a347f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "de96da566e06249f", "status": "passed", "time": {"start": 1755718320894, "stop": 1755718343451, "duration": 22557}}]}, "969451307307a13b4d89a24bb46ad0bb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e56a638a2fdc8665", "status": "passed", "time": {"start": 1755716169915, "stop": 1755716191439, "duration": 21524}}]}, "db1dca9aabe4b3e03e7003d17cf3fc9c": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "1fcee6f5e5d630ca", "status": "skipped", "statusDetails": "Skipped: airplane mode 会导致设备断开网络，先跳过", "time": {"start": 1755708816729, "stop": 1755708816729, "duration": 0}}]}, "74da34fd3c558df1234d7c0e937641b3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b3ac3f5aab7b53bc", "status": "passed", "time": {"start": 1755717691603, "stop": 1755717712579, "duration": 20976}}]}, "e1f3b35ff3714fe0225699eb84cc1b87": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e5df7cd0fdfccd2c", "status": "passed", "time": {"start": 1755711738428, "stop": 1755711764360, "duration": 25932}}]}, "eda6bef994b1b0ef78f60f433fb1d4f8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4696dac000e64935", "status": "passed", "time": {"start": 1755703578511, "stop": 1755703603431, "duration": 24920}}]}, "8f73bb4e2ab622960daf9c39a4008510": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b1ec069839ac462f", "status": "passed", "time": {"start": 1755713649156, "stop": 1755713670025, "duration": 20869}}]}, "891e31ec8bf99ceed4f462ce0c8629db": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "754158780744d744", "status": "passed", "time": {"start": 1755711375384, "stop": 1755711395648, "duration": 20264}}]}, "084048a337d3081654dc4414f67fce70": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1eee0185c02a2d72", "status": "passed", "time": {"start": 1755716495654, "stop": 1755716516984, "duration": 21330}}]}, "0a76822399c9a8e342924e5ae6cce12c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b07f145ff380d57a", "status": "passed", "time": {"start": 1755710200825, "stop": 1755710222912, "duration": 22087}}]}, "e82a80866bdbe9a7e1ac367f20c977b5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c1d759363b985608", "status": "passed", "time": {"start": 1755717971969, "stop": 1755717992485, "duration": 20516}}]}, "b165a17be8ab35920a6af9be7611a2c9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "450d5205bf19d570", "status": "passed", "time": {"start": 1755701720522, "stop": 1755701742753, "duration": 22231}}]}, "f46a1c12d07d5949dbcf4b31314824ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "90eedf7238194c12", "status": "passed", "time": {"start": 1755707148485, "stop": 1755707177983, "duration": 29498}}]}, "31c983fdcc8b62f5927f99c0482b828d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6a030dd47fe127d3", "status": "passed", "time": {"start": 1755715474874, "stop": 1755715495589, "duration": 20715}}]}, "e3cc499fef74e68f1c802e097ccd0f42": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4d31fe080feb4515", "status": "passed", "time": {"start": 1755711820163, "stop": 1755711845606, "duration": 25443}}]}, "cf0ebfd1b4e2ab43e2f516ad6a1a6917": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f77e3b68b75abd44", "status": "passed", "time": {"start": 1755704656348, "stop": 1755704685142, "duration": 28794}}]}, "89b134ac1374e88187e793daf9f8fcab": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9469bcba4556be7c", "status": "passed", "time": {"start": 1755716423585, "stop": 1755716444442, "duration": 20857}}]}, "c2ecb960f7f893feeaa2f24a34c9d77e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b50886ca1fa2d560", "status": "passed", "time": {"start": 1755711779136, "stop": 1755711805444, "duration": 26308}}]}, "5cf50058091f34fd1ed89d0b8f717355": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9e7da9e0fe709259", "status": "passed", "time": {"start": 1755709221263, "stop": 1755709269759, "duration": 48496}}]}, "8c62567d8b8a27f77124afc90fa44336": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "265b97a2f55344c7", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: [\"Okay, I've deleted the alarm for 08:00.\"]\nassert False", "time": {"start": 1755700462401, "stop": 1755700482675, "duration": 20274}}]}, "de5ff490f92fc399976d91fe0edc371e": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "24b291e1c219b3ab", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['order a takeaway', 'I need to download Yandex Eats to continue.', '', '', '', '']'\nassert False", "time": {"start": 1755710021144, "stop": 1755710041268, "duration": 20124}}]}, "cce948f7c988b6f22bd4e8d08ca74deb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "69c78d8d1530af12", "status": "passed", "time": {"start": 1755717370412, "stop": 1755717390704, "duration": 20292}}]}, "1a94a631f074dc4c0ba2bd39c9518123": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3afa7a07e2667b4b", "status": "passed", "time": {"start": 1755705670212, "stop": 1755705691592, "duration": 21380}}]}, "e60dab4e55edacbecf632d4d22f368e6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "76959a0932e6df2a", "status": "passed", "time": {"start": 1755715874734, "stop": 1755715896190, "duration": 21456}}]}, "04263bf3ad5402ebd901c9c0e6682325": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "54fb5a2d17f75684", "status": "passed", "time": {"start": 1755712299813, "stop": 1755712321431, "duration": 21618}}]}, "728822fc623e888cd9efa450f4737787": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d0df3bdd8860ce09", "status": "passed", "time": {"start": 1755710237336, "stop": 1755710258435, "duration": 21099}}]}, "a34b87ce6db1ff744d0ab6c172eb93da": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6463e34a97199c46", "status": "skipped", "statusDetails": "Skipped: 重启会导致设备断开，先跳过", "time": {"start": 1755715568959, "stop": 1755715568959, "duration": 0}}]}, "6af3d12b439ba44d7cce5c3d3ba19e86": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "9ca4f25a9ca3a301", "status": "skipped", "statusDetails": "Skipped: reboot 会导致设备断开，先跳过", "time": {"start": 1755715461198, "stop": 1755715461198, "duration": 0}}]}, "7413abdce214459d0e44671ef65b660b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c8fa830383111da9", "status": "passed", "time": {"start": 1755705818835, "stop": 1755705851636, "duration": 32801}}]}, "bfd4a9e37b70dca0b14b0ccf5246fc4a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cb9480142c2cdbd4", "status": "passed", "time": {"start": 1755713336030, "stop": 1755713356947, "duration": 20917}}]}, "f7282303534c1c8599c3343608e6f453": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e33e43fb5c8a4fcf", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The high is forecast as', 'and the low as', '℃']\nassert False", "time": {"start": 1755702437492, "stop": 1755702458309, "duration": 20817}}]}, "1498285b0d63f23df3a22c9c5262f7f3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b5b43cb03a6debaa", "status": "passed", "time": {"start": 1755705281263, "stop": 1755705393269, "duration": 112006}}]}, "80dab16fde357aadc4387b5d440ed276": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "7ecfb565ec6b27a6", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only', 'WhatsApp is not installed yet']，实际响应: '['send my recent photos to mom through whatsapp', 'I need to download WhatsApp to continue.', '', '', '', '']'\nassert False", "time": {"start": 1755715768462, "stop": 1755715789733, "duration": 21271}}]}, "ea48444c2b0789e59a64850ccfab3722": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e934b9740d43200f", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The following event has been added for you.']\nassert False", "time": {"start": 1755700420879, "stop": 1755700447628, "duration": 26749}}]}, "7d3b4e67344145885187c529ee88a9aa": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ee2ca97846776ba6", "status": "passed", "time": {"start": 1755716459388, "stop": 1755716480480, "duration": 21092}}]}, "ca9dd7f70b2888aafceb94247d7986f0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "855de268ec29413f", "status": "passed", "time": {"start": 1755716684678, "stop": 1755716705924, "duration": 21246}}]}, "cd2650c8b690a339f6c3696b18b9dc0d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "500f2d3edf3668e6", "status": "passed", "time": {"start": 1755707461475, "stop": 1755707482773, "duration": 21298}}]}, "7fb2c589f1fda51205a5af1b549d5045": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bfe2edf9ead1e168", "status": "passed", "time": {"start": 1755705780394, "stop": 1755705804190, "duration": 23796}}]}, "19fa56a92b4343c1894780564290d112": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "b09602dc5cc09fc5", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['WhatsApp is not installed yet']\nassert False", "time": {"start": 1755713492907, "stop": 1755713521466, "duration": 28559}}]}, "54b47105d42d2a9f18eec071fba40c73": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f076e424c2a10ed7", "status": "passed", "time": {"start": 1755700251724, "stop": 1755700286265, "duration": 34541}}]}, "56528a816381bba6ed1ca007f557362f": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "42e22ad7e13fa77", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755712336003, "stop": 1755712356170, "duration": 20167}}]}, "f05a6eb960fbbc415e4c605538080373": {"statistic": {"failed": 0, "broken": 1, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "684ccf6e53e263d3", "status": "broken", "statusDetails": "ValueError: too many values to unpack (expected 3)", "time": {"start": 1755703657746, "stop": 1755703677263, "duration": 19517}}]}, "0f4d3881c287fa46a9fdaf099fc19f8d": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "34d823de203fd775", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['turn on auto rotate screen', 'Portrait lock is turned off now.', 'Portrait lock', '', '', '']'\nassert None", "time": {"start": 1755708829957, "stop": 1755708850102, "duration": 20145}}]}, "5d0a6cda1787168fa2fdaaae1dee86f3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f4ec35d405befac3", "status": "passed", "time": {"start": 1755712512018, "stop": 1755712532438, "duration": 20420}}]}, "2b2dcc407b5c428f968f62d94fe8025c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8f4ec68a68e1e48d", "status": "passed", "time": {"start": 1755716133912, "stop": 1755716155214, "duration": 21302}}]}, "ae0ee984c3712fd05ea04b52289e14fe": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "96e71235bdda612e", "status": "passed", "time": {"start": 1755700654244, "stop": 1755700684707, "duration": 30463}}]}, "7c32e753573a480d7d5c09abab43469e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "99484f3a117fde9b", "status": "passed", "time": {"start": 1755700746451, "stop": 1755700783074, "duration": 36623}}]}, "a3c84dd7a2924ee198fdb33cbc4e20b6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5f8ee9e3f5d79dcb", "status": "passed", "time": {"start": 1755710835886, "stop": 1755710864916, "duration": 29030}}]}, "918c52f1eb9803594ff76c724b43d5f8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2f8312ea7202df42", "status": "passed", "time": {"start": 1755709690853, "stop": 1755709721504, "duration": 30651}}]}, "cc44ad4097a589726631a345e0cd01ad": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8d93d331a494628b", "status": "passed", "time": {"start": 1755702557725, "stop": 1755702579666, "duration": 21941}}]}, "c3dde525f6a284fe4a3e4b670182329f": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ee618ddd3097a2de", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.']，实际响应: '['merry christmas', 'Merry Christmas to you', '', '', '', '']'\nassert False", "time": {"start": 1755714254873, "stop": 1755714278250, "duration": 23377}}]}, "e32881dd9d54414fa74d523ef27b055c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3ea651471ee18f9a", "status": "passed", "time": {"start": 1755708935625, "stop": 1755708956205, "duration": 20580}}]}, "488c24d02f5d5348f35881280e505f32": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "167590b8047e5e71", "status": "failed", "statusDetails": "AssertionError: GoogleMap应用未打开: 初始=False, 最终=False, 响应='['navigation to the address in thie image', '', '', '', 'I am sorry, I am unable to perform this task.', 'Generated by AI, for reference only', 'Dialogue', '02:27 Dialogue Explore Swipe down to view earlier chats <PERSON><PERSON>: V<PERSON>l Busker Faces Harassment What is Ask About Screen? S. Africa Eases Starlink Entry navigation to the address in thie image I am sorry, I am unable to perform this task. Generated by AI, for reference only Can you suggest nearby places? How to share the address? I need directions now. DeepSeek-R1 Feel free to ask me any questions…']'\nassert False", "time": {"start": 1755714406421, "stop": 1755714432815, "duration": 26394}}]}, "ab0169efb30d26689cbce230ff455598": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "66ef2627b330a8b5", "status": "passed", "time": {"start": 1755715195542, "stop": 1755715228148, "duration": 32606}}]}, "cf1bdc2b1d9b604939681e5b6ac6f506": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "5bfde334bbb93da9", "status": "failed", "statusDetails": "AssertionError: 初始=None, 最终=None, 响应='['turn on smart reminder', 'Smart reminder is turned on now.', 'Smart reminder', '', '', '']'\nassert None", "time": {"start": 1755709113798, "stop": 1755709134251, "duration": 20453}}]}, "963c2cd1bdb409e4cfe9589a18006e88": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1298311829448811", "status": "passed", "time": {"start": 1755706699791, "stop": 1755706720799, "duration": 21008}}]}, "a5a3cc08eb97e600c97acb65a7439ec0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bf86f908019b6fe4", "status": "passed", "time": {"start": 1755716934664, "stop": 1755716955823, "duration": 21159}}]}, "bd9c64dabd06671b98d60748492be267": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4cc7766d33a1432f", "status": "passed", "time": {"start": 1755701870600, "stop": 1755701900365, "duration": 29765}}]}, "c2a64f07232d43585d1dfee25c2f9407": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "afe67ecef8a98cc0", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['°C', 'Generated by AI, for reference only']，实际响应: '[\"what's the wheather today?\", 'Sorry, weather data error, try later.', '', '', '', '']'\nassert False", "time": {"start": 1755704434485, "stop": 1755704454493, "duration": 20008}}]}, "a84e06839a37b7806fefd316aa632437": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d216d263e31042c2", "status": "passed", "time": {"start": 1755712687722, "stop": 1755712712211, "duration": 24489}}]}, "8b6d374ba70006ef7591c8e0bf72bb00": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "92b833200938e00b", "status": "passed", "time": {"start": 1755717404869, "stop": 1755717425131, "duration": 20262}}]}, "0f3523ec9dc3ea86ebaca76b6956f01c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "45df33e0046924ed", "status": "passed", "time": {"start": 1755705741223, "stop": 1755705765810, "duration": 24587}}]}, "489e5c631d3a3ce20f77bce4c7c6632a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9e482f7900f6f786", "status": "passed", "time": {"start": 1755707313355, "stop": 1755707335982, "duration": 22627}}]}, "329fa4b06eb0b0d769c2c418ed03dab7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6fef0ab2837f6386", "status": "passed", "time": {"start": 1755701575688, "stop": 1755701597710, "duration": 22022}}]}, "66496441d7401e453a580b4a8c23d111": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "260c2eeb61aaf9d7", "status": "passed", "time": {"start": 1755706954201, "stop": 1755706977150, "duration": 22949}}]}, "169e5b613c0fec2cebd053175998bf17": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6d7775be5b587cf2", "status": "failed", "statusDetails": "AssertionError: clock: 初始=None, 最终=None, 响应='['open clock', 'Done!', '', '', '', '', '[com.transsion.deskclock页面内容] 闹钟 | 06:00 | 周一至周五 | 07:00 | 周日, 周六 | 闹钟 | 世界时钟 | 定时器 | 秒表']'\nassert None", "time": {"start": 1755700702631, "stop": 1755700731127, "duration": 28496}}]}, "a5e15bb05795c5949d67f33200f4d69b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f9845e8245082057", "status": "passed", "time": {"start": 1755706527526, "stop": 1755706549367, "duration": 21841}}]}, "37d8f85ba7c46a46b390c4fc5ab20de7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9834e78b2b998f9d", "status": "passed", "time": {"start": 1755700798110, "stop": 1755700819231, "duration": 21121}}]}, "44b646d68146a0c48da2623a58b17f6f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "73d9c98de41b4a4c", "status": "passed", "time": {"start": 1755701504496, "stop": 1755701524938, "duration": 20442}}]}, "4f538fc772535a0c0811ad87d3aa9494": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "be5325f87bc04cd5", "status": "passed", "time": {"start": 1755716649188, "stop": 1755716669905, "duration": 20717}}]}, "b12ee4e4a5d7e51ba0abe166b6c90352": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "12d1457f5f2c6ee2", "status": "passed", "time": {"start": 1755715149162, "stop": 1755715181119, "duration": 31957}}]}, "8814f1dafa698e785ee1f58faa6e745d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e3084c3fc9a93c05", "status": "passed", "time": {"start": 1755715619742, "stop": 1755715640804, "duration": 21062}}]}, "309f3fbb8586cc15e324e94cec37e7ea": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "8b9194a09068301f", "status": "passed", "time": {"start": 1755702062873, "stop": 1755702085196, "duration": 22323}}]}, "08bb1ce458e948b9a8084fb3ec1f2c83": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "16d9c17ea798fd08", "status": "passed", "time": {"start": 1755713190510, "stop": 1755713210819, "duration": 20309}}]}, "d9f01ef1af79559082ce9e9b2e40295f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ace44e88029ee698", "status": "passed", "time": {"start": 1755700203682, "stop": 1755700236832, "duration": 33150}}]}, "0e1d64e0daaaf11983cdc488ea4b0993": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d9e70fc4fdc0e68a", "status": "skipped", "statusDetails": "Skipped: power off 会导致设备断开，先跳过", "time": {"start": 1755715388628, "stop": 1755715388628, "duration": 0}}]}, "984a0fd313bba0ca2f20f0bbff732eb8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b50ecabc6cc24973", "status": "passed", "time": {"start": 1755717832334, "stop": 1755717852489, "duration": 20155}}]}, "e5dc64184617a9497ec52a3b74103b55": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fa0d98e48edee82d", "status": "passed", "time": {"start": 1755706236836, "stop": 1755706258231, "duration": 21395}}]}, "981b71ad744b9a603c31ab4832ff9439": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "57d7096266b039f9", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['document provides a summary of the application']\nassert False", "time": {"start": 1755705533332, "stop": 1755705583011, "duration": 49679}}]}, "0d9f0969c1336e077b7ada5963a2516a": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d3ccb51a7e3071bf", "status": "skipped", "statusDetails": "Skipped: 该脚本较特殊，先跳过", "time": {"start": 1755705962643, "stop": 1755705962643, "duration": 0}}]}, "461fa25c15c0a862f353e5384438bc5d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d407cfa53b8805dd", "status": "passed", "time": {"start": 1755713014263, "stop": 1755713035343, "duration": 21080}}]}, "3f0254c16b7bc20d18094d502f49138a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "377e209c6953434d", "status": "passed", "time": {"start": 1755713407064, "stop": 1755713427864, "duration": 20800}}]}, "aac47e3ff4229b41a579f7c9ec938afb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2053024043889923", "status": "passed", "time": {"start": 1755703149714, "stop": 1755703173141, "duration": 23427}}]}, "a084b34b62992b8f17deb64af6e57e39": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5b51fa563fcca0d", "status": "passed", "time": {"start": 1755718358035, "stop": 1755718379739, "duration": 21704}}]}, "600ddf60808e2a751a4a4742a65811c7": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c5bdc9fc6c1955b3", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['WhatsApp is not installed yet']\nassert False", "time": {"start": 1755718006489, "stop": 1755718035885, "duration": 29396}}]}, "8c5a5747e91f2cb0412111d5027bb7ec": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "70b4795c0bc7ede2", "status": "passed", "time": {"start": 1755709184668, "stop": 1755709206538, "duration": 21870}}]}, "5b1e68388004fe021690469c3d83b485": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "845234b003a4566f", "status": "failed", "statusDetails": "AssertionError: clock: 初始=None, 最终=None, 响应='[\"set alarm for 10 o'clock\", 'Alarm for \"10:00\" has been set.', '', '', '', '']'\nassert None", "time": {"start": 1755707193006, "stop": 1755707214799, "duration": 21793}}]}, "ca269ac93364dc6180676f5680956b32": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "cf23a689f9253a88", "status": "passed", "time": {"start": 1755715402341, "stop": 1755715423166, "duration": 20825}}]}, "e4bab2ec1074fdbc8b4508dfad12adfc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "52deae9f27e577b1", "status": "passed", "time": {"start": 1755706636094, "stop": 1755706685028, "duration": 48934}}]}, "afde8e86697e1ec7ad65ac0c993e60f2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "827822ecc26ee170", "status": "passed", "time": {"start": 1755708722461, "stop": 1755708745247, "duration": 22786}}]}, "89e187687dfa3317845107c44a62f287": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3d2e13bc086ec514", "status": "passed", "time": {"start": 1755712979640, "stop": 1755712999712, "duration": 20072}}]}, "bf94eb080274f830f3097dd5adde1ed1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f86949e3bb15b41f", "status": "passed", "time": {"start": 1755708505469, "stop": 1755708526824, "duration": 21355}}]}, "fd79b0d35f1f4639521f70b269d3aadc": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b52b8c7414d02808", "status": "passed", "time": {"start": 1755714621284, "stop": 1755714642428, "duration": 21144}}]}, "09f397887d36f3ef1e86e3c78272f1d6": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8332a4b64b5294bb", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['order a burger', 'I need to download Yandex Eats to continue.', '', '', '', '']'\nassert False", "time": {"start": 1755709986074, "stop": 1755710006183, "duration": 20109}}]}, "d18ed3013dc7867440fb611fb474ca05": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1e30d8eaa155c870", "status": "passed", "time": {"start": 1755705938584, "stop": 1755705961241, "duration": 22657}}]}, "599b7a465f619c38a4638073f59c38c0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2228f510d1fec9a0", "status": "passed", "time": {"start": 1755717936863, "stop": 1755717957311, "duration": 20448}}]}, "c612b04b455e8fbc03acd18c2fc89827": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4d6edbca908730fe", "status": "passed", "time": {"start": 1755712617136, "stop": 1755712637767, "duration": 20631}}]}, "16f38913a9d7e0ffc4c5ac51d5acf5c7": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4b4ca06394093464", "status": "passed", "time": {"start": 1755708970871, "stop": 1755708991635, "duration": 20764}}]}, "8bf93fd8ac952a757cc694ecc78b8d51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fb609f333dbf8703", "status": "passed", "time": {"start": 1755700497842, "stop": 1755700527970, "duration": 30128}}]}, "18fb8c43c609a9825fe52e528761fd1b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b304259457fa7774", "status": "passed", "time": {"start": 1755707070491, "stop": 1755707098024, "duration": 27533}}]}, "519d11d818a361bc75d5af94c6a68b28": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fc2a1d7492dfd853", "status": "passed", "time": {"start": 1755704504441, "stop": 1755704526816, "duration": 22375}}]}, "700a4f81d53d76c265778c230c99dd8c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "17d74d81d04a6a9d", "status": "passed", "time": {"start": 1755712546685, "stop": 1755712567065, "duration": 20380}}]}, "b9bb05ac1dcf8926da63d4ecbb1524cd": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5524ec83adbefa12", "status": "passed", "time": {"start": 1755705900799, "stop": 1755705923362, "duration": 22563}}]}, "57a9b2e6f318afd186b838ed42ebd55c": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6ac4bcf2b2320edd", "status": "passed", "time": {"start": 1755712157747, "stop": 1755712180117, "duration": 22370}}]}, "e21c5dda6a9f09862a68c3a0bcda554a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a3129dbca1ab87c5", "status": "passed", "time": {"start": 1755716755460, "stop": 1755716776279, "duration": 20819}}]}, "dc901cadfe1de0042de7c0f7461a804e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f76e37bafb506cca", "status": "passed", "time": {"start": 1755711126465, "stop": 1755711147400, "duration": 20935}}]}, "6ba20fdcdbf83dd327ea91bd93e697e0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3cf2f607644999ee", "status": "passed", "time": {"start": 1755714565982, "stop": 1755714606300, "duration": 40318}}]}, "e1ef5de48cb99781fc16bc01be62dca2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2c1bb67e3f12871c", "status": "passed", "time": {"start": 1755713442091, "stop": 1755713478381, "duration": 36290}}]}, "543965b4120af95548616c95b1b70ef1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "159b858661233ae7", "status": "passed", "time": {"start": 1755704016473, "stop": 1755704038350, "duration": 21877}}]}, "07dafe2b3e3ed9841a34e9fd19de58be": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5da2150a89012bb7", "status": "passed", "time": {"start": 1755717335502, "stop": 1755717356195, "duration": 20693}}]}, "c04d9357fdaf44e6ee27f8a97ece6c5d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "488bf54883b7334d", "status": "passed", "time": {"start": 1755716276958, "stop": 1755716297527, "duration": 20569}}]}, "5d0294174a7d609e38392f61f2170810": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c4331d4423266ee9", "status": "passed", "time": {"start": 1755711056075, "stop": 1755711076900, "duration": 20825}}]}, "3004e41c81a7ebd857f79d043aaf59df": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "8bce5d4f21a14189", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['The high is forecast as', 'and the low as', '℃']\nassert False", "time": {"start": 1755702396253, "stop": 1755702422756, "duration": 26503}}]}, "d960192ea83ce0c13a534ec13ca1700e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "fb2c209e944023e8", "status": "passed", "time": {"start": 1755704242504, "stop": 1755704263386, "duration": 20882}}]}, "fa8d6ac5c42acf5f644e6f5370a9a773": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e814f84e67086f9f", "status": "passed", "time": {"start": 1755711339752, "stop": 1755711360642, "duration": 20890}}]}, "544fc8b021d2dbcaf295cd05b798f816": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "91b05fae09e97bb2", "status": "passed", "time": {"start": 1755716899062, "stop": 1755716919863, "duration": 20801}}]}, "1470cf4116a3328d5a8812cd15bb56f8": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3f6b89daaeb1bc0e", "status": "passed", "time": {"start": 1755709463485, "stop": 1755709484276, "duration": 20791}}]}, "53ec4c77118606257c016fc2f7b22065": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "50a9bd26888475a8", "status": "passed", "time": {"start": 1755701757661, "stop": 1755701785974, "duration": 28313}}]}, "85b61394b4b07c85faf6e5081371fbf2": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "abd7bf1438f0a7d2", "status": "passed", "time": {"start": 1755708686834, "stop": 1755708707905, "duration": 21071}}]}, "1695232002b2ad29ffa1faf52965470d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "960cdd0515597ad8", "status": "passed", "time": {"start": 1755711667964, "stop": 1755711688997, "duration": 21033}}]}, "9fcc7f87aa3845b28893959bf17baa2b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f0918076a332776f", "status": "passed", "time": {"start": 1755717653718, "stop": 1755717676679, "duration": 22961}}]}, "e1b97b8698ff620d6d8faf32f381c874": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "cc7da05a519e4d8f", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['The following images are generated for you.', '%']，实际响应: '['Generate a landscape painting image for me', \"You've reached the image generation limit for today.\", '', '', '', '']'\nassert False", "time": {"start": 1755712476327, "stop": 1755712497053, "duration": 20726}}]}, "2060dd1cfd03194548c0456a10798266": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5d999b8da9f2eecc", "status": "passed", "time": {"start": 1755702171420, "stop": 1755702194995, "duration": 23575}}]}, "0dc117f78053bbddaf3ffcf69df0af9d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9b6f40913c54816", "status": "passed", "time": {"start": 1755715284958, "stop": 1755715312114, "duration": 27156}}]}, "43a8e6496d8d78f2b8bc066858f8bdd9": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "a981a970f4f7d352", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach']，实际响应: '['order a takeaway', 'I need to download Yandex Eats to continue.', '', '', '', '']'\nassert False", "time": {"start": 1755714905123, "stop": 1755714925899, "duration": 20776}}]}, "569e770c250388bfbcf64d0cbbb8b351": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2cadbc215e77f7e9", "status": "passed", "time": {"start": 1755710985078, "stop": 1755711006198, "duration": 21120}}]}, "7c74ed3e622ad29dd79be61222ad59bc": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c4b327dbe29a37ce", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['oops']\nassert False", "time": {"start": 1755707497944, "stop": 1755707518821, "duration": 20877}}]}, "e7de7a828ef1b59725204585ed7e1d64": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6fbccee76b8de3ce", "status": "passed", "time": {"start": 1755716575019, "stop": 1755716598284, "duration": 23265}}]}, "b28e20f7b76af65c54477681eee78169": {"statistic": {"failed": 0, "broken": 0, "skipped": 1, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "92bd1b35636073c0", "status": "skipped", "statusDetails": "Skipped: make a phone call to 17621905233命令需要先添加联系人，才能拨打电话，无法通用化", "time": {"start": 1755714241421, "stop": 1755714241421, "duration": 0}}]}, "acdb323f998d6127fbebbc545f6e8a59": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d358252686d0a9f1", "status": "passed", "time": {"start": 1755700954911, "stop": 1755700988454, "duration": 33543}}]}, "c076d6e18e779bfeb810e69b30339aa2": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "796644c4e7b3e78e", "status": "failed", "statusDetails": "AssertionError: 文件不存在！\nassert False", "time": {"start": 1755708268047, "stop": 1755708311973, "duration": 43926}}]}, "edbb2ef8b0440e0325be2bfae4eb0bee": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "d6a1fd0c70f47e0f", "status": "passed", "time": {"start": 1755711410121, "stop": 1755711430728, "duration": 20607}}]}, "d235f5ef7da67b833ad362b7c693c8f1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dd6625e7baf2349d", "status": "passed", "time": {"start": 1755709149214, "stop": 1755709170312, "duration": 21098}}]}, "8373737839693413a37e35b627a0f5de": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "ffcc91edf810c005", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Notification volume has been set to the minimum']\nassert False", "time": {"start": 1755708469483, "stop": 1755708490778, "duration": 21295}}]}, "4cfe8e55b2a91a62bbf1141ffc0cc530": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "63fa6ccec7fad5eb", "status": "passed", "time": {"start": 1755711304552, "stop": 1755711324987, "duration": 20435}}]}, "d8a01bf3d9b9092622318d8d22f17d9e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "922bf33b821ac18f", "status": "passed", "time": {"start": 1755710651429, "stop": 1755710672906, "duration": 21477}}]}, "511b4baaab6d7793eefd3f92b3a77d8b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c8796ec65c91d6b6", "status": "passed", "time": {"start": 1755706772154, "stop": 1755706793603, "duration": 21449}}]}, "d18ea588937139bb162adb1092a66013": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "df4ac9080a8fd69e", "status": "passed", "time": {"start": 1755709284284, "stop": 1755709305126, "duration": 20842}}]}, "b3fa1d22b59def5f059cd9b0eefbe2b0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a6a2d00c2b278d2", "status": "passed", "time": {"start": 1755702283509, "stop": 1755702305954, "duration": 22445}}]}, "548362627ce690e10e5f8ca35d247c62": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c9b4155386854041", "status": "passed", "time": {"start": 1755706599479, "stop": 1755706621212, "duration": 21733}}]}, "359341836df6d43ec99426e6918dc6ca": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "35be0a29733330f", "status": "passed", "time": {"start": 1755705189398, "stop": 1755705266614, "duration": 77216}}]}, "861f0c94cdad5a5d60cd9fc71e2429d6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "5fd4997f0e350915", "status": "passed", "time": {"start": 1755701988813, "stop": 1755702011621, "duration": 22808}}]}, "30903d6e764eebda77a45c5af4464d00": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "df7783d41ebacf14", "status": "passed", "time": {"start": 1755703910928, "stop": 1755703931783, "duration": 20855}}]}, "7ba4a9d343c0f63e9f654ce03ea4fa51": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "bd37238f23d42cc1", "status": "passed", "time": {"start": 1755716827173, "stop": 1755716848310, "duration": 21137}}]}, "5eaa5a3015ce02f75c4c021fdbd2f78d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "876cbdb653c40bdb", "status": "passed", "time": {"start": 1755715582138, "stop": 1755715604713, "duration": 22575}}]}, "42bb23fa5566b20ae050e85bbee099ef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "dc9110b4876a727d", "status": "passed", "time": {"start": 1755712000187, "stop": 1755712021067, "duration": 20880}}]}, "434b905bf8be3ce2ee79606468e155db": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "40f21bb9bcc58571", "status": "passed", "time": {"start": 1755713608235, "stop": 1755713634587, "duration": 26352}}]}, "fca782bf64e9cf595a09003471d4cc31": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "ad8b964c2c7277f2", "status": "passed", "time": {"start": 1755713976957, "stop": 1755713997713, "duration": 20756}}]}, "d4409d015660212a7021f4aa7f849f30": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "4772631ef1362a3", "status": "passed", "time": {"start": 1755710451241, "stop": 1755710471748, "duration": 20507}}]}, "6df22ddc82daabcf8389e60296dc694e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "c2bccb74d1e66bcd", "status": "passed", "time": {"start": 1755703731070, "stop": 1755703753253, "duration": 22183}}]}, "d28e2beb1494e42c051e9b99add608fb": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "19f2756da55e22b9", "status": "passed", "time": {"start": 1755703503992, "stop": 1755703525473, "duration": 21481}}]}, "861aa58f9a3d0d9c9861d88316e784c5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "99fa151c33c67f6d", "status": "passed", "time": {"start": 1755701003039, "stop": 1755701023560, "duration": 20521}}]}, "6b4c2fb43e48aa6ef45b7a33c8b2d9ef": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "66394b6d7c195dfe", "status": "passed", "time": {"start": 1755715438128, "stop": 1755715459735, "duration": 21607}}]}, "cda905ef365af8bbcc5fba28f6bde9ea": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "11f0b98ab62a7ad1", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording finished']\nassert False", "time": {"start": 1755707711049, "stop": 1755707731566, "duration": 20517}}]}, "bc062eca91b16841cac5c9865921b5c1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3feaf46a9f9c6a7c", "status": "passed", "time": {"start": 1755700350192, "stop": 1755700371063, "duration": 20871}}]}, "a455fee07fb3c7d389380cb95d9a092c": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "84e86790fa4b5099", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['WhatsApp is not installed ye']\nassert False", "time": {"start": 1755714165311, "stop": 1755714194613, "duration": 29302}}]}, "ff8d76af98b9fdfaa206acbf87daa843": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "35dde70f35bbfe9b", "status": "passed", "time": {"start": 1755712079475, "stop": 1755712107770, "duration": 28295}}]}, "4a00cb3818a7086991fb9f7a4d2a3bb5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "58260e42e810e85e", "status": "passed", "time": {"start": 1755718214394, "stop": 1755718235286, "duration": 20892}}]}, "1da800483d0bd7f8dbe657a8d5c37f76": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9b44bb9381e76543", "status": "passed", "time": {"start": 1755711196322, "stop": 1755711217227, "duration": 20905}}]}, "81f981a4ddbff762d2de1cd977c5568a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "6f18513442b4a26d", "status": "passed", "time": {"start": 1755712441418, "stop": 1755712461689, "duration": 20271}}]}, "6f7052acfdd45e34e5dded44ad87416e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2e87037a25b53754", "status": "passed", "time": {"start": 1755706381481, "stop": 1755706404917, "duration": 23436}}]}, "fdcf3737e32a4361e11902caf25fed5f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "901e910a93d74ccd", "status": "passed", "time": {"start": 1755708541586, "stop": 1755708564338, "duration": 22752}}]}, "00e9182de9c9d3297d90ff42d6771a57": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "7988511e8b72ea8", "status": "passed", "time": {"start": 1755716388567, "stop": 1755716409142, "duration": 20575}}]}, "abcb20b3882e9c0dbf1add7f63082581": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "70e89ac9f7ab66d7", "status": "failed", "statusDetails": "AssertionError: 响应文本应包含['Sorry', 'Oops', 'out of my reach', 'Generated by AI, for reference only']，实际响应: '['pls open whatsapp', 'I need to download whatsapp messenger to continue.', '', '', '', '']'\nassert False", "time": {"start": 1755715363903, "stop": 1755715386911, "duration": 23008}}]}, "1ed60306ec6b3749ffacc2d410664db2": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "e3ecb8cc47bce00f", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['AIGC', 'document provides a summary of the application']\nassert False", "time": {"start": 1755704955804, "stop": 1755705043062, "duration": 87258}}]}, "d41a9c89a400d807309a9cecf36c0728": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "478e1e1d32621f8c", "status": "passed", "time": {"start": 1755701381405, "stop": 1755701413575, "duration": 32170}}]}, "a6002c930e7d5977f441f6060af6308d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f2b00b9e13a3df07", "status": "passed", "time": {"start": 1755702859443, "stop": 1755702879903, "duration": 20460}}]}, "6c315a350a546e1382e435255d28245b": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "16b942322a4ba58", "status": "passed", "time": {"start": 1755716312255, "stop": 1755716332960, "duration": 20705}}]}, "d8bd499fa9e4e04741c5c255fac9036d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e74646cd5997be55", "status": "passed", "time": {"start": 1755714054802, "stop": 1755714075379, "duration": 20577}}]}, "936ae2bf6db744b69d4acf28b22f7646": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1a1358239fa47472", "status": "passed", "time": {"start": 1755700833480, "stop": 1755700866973, "duration": 33493}}]}, "436193bc4e8c44d21b6520da0589f88d": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "2e2cd6de3dca64ae", "status": "passed", "time": {"start": 1755713825711, "stop": 1755713847128, "duration": 21417}}]}, "b2f52f3c587a626460e5698cac861baf": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "c0d0be5b36a40ca4", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Done']\nassert False", "time": {"start": 1755708046384, "stop": 1755708077156, "duration": 30772}}]}, "78164cec6ab8359be9416229a4882ef9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e43d803ea41dae27", "status": "passed", "time": {"start": 1755701914884, "stop": 1755701938613, "duration": 23729}}]}, "f06396240414bb7ac3c5c049002eef1e": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "f002a91a86cc5d52", "status": "passed", "time": {"start": 1755715691494, "stop": 1755715718074, "duration": 26580}}]}, "78de5607a6208f59723ba6cf4fcf09c4": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "3688f63a779e5a30", "status": "passed", "time": {"start": 1755702594293, "stop": 1755702615786, "duration": 21493}}]}, "bda3c1f82ca5c246958b5bf50db09a67": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "46f8645a9e56b76a", "status": "passed", "time": {"start": 1755717225156, "stop": 1755717245928, "duration": 20772}}]}, "eda16efd838471b84f33f12ec91662c9": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "54a6c07f8721dfed", "status": "passed", "time": {"start": 1755715804224, "stop": 1755715824936, "duration": 20712}}]}, "fbd0b78d6e1c10d8dc70e2bd96aa5bdc": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "bcfaaf58d103c438", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Your current location']\nassert False", "time": {"start": 1755715326769, "stop": 1755715348909, "duration": 22140}}]}, "45b57073b776ed5666f2f20a47a4638f": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "203fe3bef4728bf3", "status": "passed", "time": {"start": 1755708011086, "stop": 1755708032167, "duration": 21081}}]}, "454f04318d433db60e7e6f2de5790fc3": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "d60c330ba8cd6757", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['Screen recording continued']\nassert False", "time": {"start": 1755707675377, "stop": 1755707696328, "duration": 20951}}]}, "9375a7a6d7b67755564dec18857d7c65": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "1338a64919f814e3", "status": "passed", "time": {"start": 1755710792191, "stop": 1755710821179, "duration": 28988}}]}, "c45aa63628fe12b375ba7e65c39d93b1": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "e0b1e46089f66afe", "status": "passed", "time": {"start": 1755704053355, "stop": 1755704074002, "duration": 20647}}]}, "cc158f7c05891fbac271a86692bc57a6": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "a247510454373d34", "status": "passed", "time": {"start": 1755717761136, "stop": 1755717781768, "duration": 20632}}]}, "fae56e9bcf9e0511ef4a7c93775731e3": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "b47ff2b67a74943e", "status": "passed", "time": {"start": 1755710093623, "stop": 1755710116183, "duration": 22560}}]}, "85e8e199dba3ac2c9d89e132805a4404": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "f83e9d3340e3be7e", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['not installed yet. Please download the app and try again.']\nassert False", "time": {"start": 1755703224996, "stop": 1755703256431, "duration": 31435}}]}, "29390733aaf67e070f7c061b70bad8a5": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "34918f91ba99a1ce", "status": "passed", "time": {"start": 1755705705901, "stop": 1755705726833, "duration": 20932}}]}, "228f8713a07d6ddbb8729f2f567c21d0": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "9303e393f4bece4c", "status": "passed", "time": {"start": 1755703347335, "stop": 1755703379238, "duration": 31903}}]}, "221d94649ab3a384e6bc24767dceba21": {"statistic": {"failed": 1, "broken": 0, "skipped": 0, "passed": 0, "unknown": 0, "total": 1}, "items": [{"uid": "6bde6f903272f86a", "status": "failed", "statusDetails": "AssertionError: 响应未包含期望内容: ['WhatsApp is not installed yet']\nassert False", "time": {"start": 1755714209846, "stop": 1755714239752, "duration": 29906}}]}, "d2d9aa669417404f06e84a7a4387c55a": {"statistic": {"failed": 0, "broken": 0, "skipped": 0, "passed": 1, "unknown": 0, "total": 1}, "items": [{"uid": "346a5de850ee3524", "status": "passed", "time": {"start": 1755707965233, "stop": 1755707996393, "duration": 31160}}]}}