{"uid": "39a8d0fd53a2c6ce", "name": "测试set smart hub返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_smart_hub.TestEllaSetSmartHub#test_set_smart_hub", "historyId": "61e923bb9b35a687b231b0c27b5ec620", "time": {"start": 1755716970970, "stop": 1755716991444, "duration": 20474}, "description": "验证set smart hub指令返回预期的不支持响应", "descriptionHtml": "<p>验证set smart hub指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755700187687, "stop": 1755700187689, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755716957351, "stop": 1755716970968, "duration": 13617}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755716970968, "stop": 1755716970968, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "验证set smart hub指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set smart hub", "time": {"start": 1755716970971, "stop": 1755716991233, "duration": 20262}, "status": "passed", "steps": [{"name": "执行命令: set smart hub", "time": {"start": 1755716970971, "stop": 1755716990995, "duration": 20024}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755716990995, "stop": 1755716991233, "duration": 238}, "status": "passed", "steps": [], "attachments": [{"uid": "680182a903b1ba73", "name": "测试总结", "source": "680182a903b1ba73.txt", "type": "text/plain", "size": 315}, {"uid": "806e5c99124e8854", "name": "test_completed", "source": "806e5c99124e8854.png", "type": "image/png", "size": 202268}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1755716991233, "stop": 1755716991235, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755716991235, "stop": 1755716991443, "duration": 208}, "status": "passed", "steps": [], "attachments": [{"uid": "fac3762ffc4aa502", "name": "测试总结", "source": "fac3762ffc4aa502.txt", "type": "text/plain", "size": 315}, {"uid": "98ae5ee98cc9a39f", "name": "test_completed", "source": "98ae5ee98cc9a39f.png", "type": "image/png", "size": 202125}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "e44c112149bace26", "name": "stdout", "source": "e44c112149bace26.txt", "type": "text/plain", "size": 11722}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755716991445, "stop": 1755716991445, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755716991448, "stop": 1755716992980, "duration": 1532}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755718486220, "stop": 1755718486223, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "story", "value": "不支持的指令"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_smart_hub"}, {"name": "subSuite", "value": "TestEllaSetSmartHub"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "5180-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_smart_hub"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "39a8d0fd53a2c6ce.json", "parameterValues": []}