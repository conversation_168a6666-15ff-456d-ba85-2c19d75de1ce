{"uid": "f2bfad1ddd35325d", "name": "测试Add the images and text on the screen to the note", "fullName": "testcases.test_ella.unsupported_commands.test_Add_the_images_and_text_on_the_screen_to_the_note.TestEllaOpenPlayPoliticalNews#test_Add_the_images_and_text_on_the_screen_to_the_note", "historyId": "5cc849d46714fff99c626e94dc28932d", "time": {"start": 1755710130878, "stop": 1755710151458, "duration": 20580}, "description": "测试Add the images and text on the screen to the note指令", "descriptionHtml": "<p>测试Add the images and text on the screen to the note指令</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1755700187687, "stop": 1755700187689, "duration": 2}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1755710117578, "stop": 1755710130876, "duration": 13298}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1755710130876, "stop": 1755710130877, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "testStage": {"description": "测试Add the images and text on the screen to the note指令", "status": "passed", "steps": [{"name": "执行命令: Add the images and text on the screen to the note", "time": {"start": 1755710130878, "stop": 1755710151231, "duration": 20353}, "status": "passed", "steps": [{"name": "执行命令: Add the images and text on the screen to the note", "time": {"start": 1755710130878, "stop": 1755710150979, "duration": 20101}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755710150979, "stop": 1755710151231, "duration": 252}, "status": "passed", "steps": [], "attachments": [{"uid": "18a422fa35b368ba", "name": "测试总结", "source": "18a422fa35b368ba.txt", "type": "text/plain", "size": 392}, {"uid": "678d80e4f87b8d55", "name": "test_completed", "source": "678d80e4f87b8d55.png", "type": "image/png", "size": 200890}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 2, "attachmentStep": false}, {"name": "验证响应包含期望内容", "time": {"start": 1755710151232, "stop": 1755710151235, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1755710151235, "stop": 1755710151457, "duration": 222}, "status": "passed", "steps": [], "attachments": [{"uid": "11cea9ea1807f3f9", "name": "测试总结", "source": "11cea9ea1807f3f9.txt", "type": "text/plain", "size": 392}, {"uid": "9b1eaa735a5ce8c7", "name": "test_completed", "source": "9b1eaa735a5ce8c7.png", "type": "image/png", "size": 200768}], "parameters": [], "attachmentsCount": 2, "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 0, "attachmentStep": false}], "attachments": [{"uid": "f44d8b73e0019517", "name": "stdout", "source": "f44d8b73e0019517.txt", "type": "text/plain", "size": 12087}], "parameters": [], "attachmentsCount": 5, "shouldDisplayMessage": false, "hasContent": true, "stepsCount": 5, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1755710151459, "stop": 1755710151459, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1755710151461, "stop": 1755710152911, "duration": 1450}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1755718486220, "stop": 1755718486223, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "attachmentsCount": 0, "shouldDisplayMessage": false, "hasContent": false, "stepsCount": 0, "attachmentStep": false}], "labels": [{"name": "severity", "value": "critical"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "打开应用"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_Add_the_images_and_text_on_the_screen_to_the_note"}, {"name": "subSuite", "value": "TestEllaOpenPlayPoliticalNews"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "5180-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_Add_the_images_and_text_on_the_screen_to_the_note"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "critical", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f2bfad1ddd35325d.json", "parameterValues": []}