{"uid": "83edc06c07f9ae9e47eb6dd1b683e4e2", "name": "packages", "children": [{"name": "testcases.test_ella", "children": [{"name": "component_coupling", "children": [{"name": "test_close_aivana", "children": [{"name": "测试close aivana能正常执行", "uid": "ace44e88029ee698", "parentUid": "0a654e8ba5d406f4dced08e54877f4f8", "status": "passed", "time": {"start": 1755700203682, "stop": 1755700236832, "duration": 33150}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0a654e8ba5d406f4dced08e54877f4f8"}, {"name": "test_close_ella", "children": [{"name": "测试close ella能正常执行", "uid": "f076e424c2a10ed7", "parentUid": "b5a2cabf288d90878566c5ce33227175", "status": "passed", "time": {"start": 1755700251724, "stop": 1755700286265, "duration": 34541}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b5a2cabf288d90878566c5ce33227175"}, {"name": "test_close_folax", "children": [{"name": "测试close folax能正常执行", "uid": "cf5abb140a3b934c", "parentUid": "3b1f73acd32162c3bc6cd626dc7f1272", "status": "passed", "time": {"start": 1755700300794, "stop": 1755700335456, "duration": 34662}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3b1f73acd32162c3bc6cd626dc7f1272"}, {"name": "test_close_phonemaster", "children": [{"name": "测试close phonemaster能正常执行", "uid": "3feaf46a9f9c6a7c", "parentUid": "e2aa70cb4f489d6ed135a8d54afbd69e", "status": "passed", "time": {"start": 1755700350192, "stop": 1755700371063, "duration": 20871}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e2aa70cb4f489d6ed135a8d54afbd69e"}, {"name": "test_continue_music", "children": [{"name": "测试continue music能正常执行", "uid": "27f56deb688575b7", "parentUid": "6a23546ce5380ec0616c9f82a57a36ad", "status": "passed", "time": {"start": 1755700385328, "stop": 1755700406429, "duration": 21101}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6a23546ce5380ec0616c9f82a57a36ad"}, {"name": "test_create_a_metting_schedule_at_tomorrow", "children": [{"name": "测试create a metting schedule at tomorrow能正常执行", "uid": "e934b9740d43200f", "parentUid": "6ef10c8d834a3fd9e2b41931a6d5b667", "status": "failed", "time": {"start": 1755700420879, "stop": 1755700447628, "duration": 26749}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6ef10c8d834a3fd9e2b41931a6d5b667"}, {"name": "test_delete_the_8_o_clock_alarm", "children": [{"name": "测试delete the 8 o'clock alarm", "uid": "265b97a2f55344c7", "parentUid": "744e7a2785bdcb064b4cb0104fffb47a", "status": "failed", "time": {"start": 1755700462401, "stop": 1755700482675, "duration": 20274}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "744e7a2785bdcb064b4cb0104fffb47a"}, {"name": "test_disable_call_on_hold", "children": [{"name": "测试disable call on hold返回正确的不支持响应", "uid": "fb609f333dbf8703", "parentUid": "77a12ec993f1aecf9afde2b5350a0a71", "status": "passed", "time": {"start": 1755700497842, "stop": 1755700527970, "duration": 30128}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "77a12ec993f1aecf9afde2b5350a0a71"}, {"name": "test_display_the_route_go_company", "children": [{"name": "测试display the route go company", "uid": "1bfcffb98f4a4154", "parentUid": "e22797f7f98b084598882fb5cec440fe", "status": "passed", "time": {"start": 1755700542223, "stop": 1755700568328, "duration": 26105}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e22797f7f98b084598882fb5cec440fe"}, {"name": "test_my_phone_is_too_slow", "children": [{"name": "测试my phone is too slow能正常执行", "uid": "5c18fefd24f91a0d", "parentUid": "9df0629aeb726d588255055629526bcb", "status": "passed", "time": {"start": 1755700582867, "stop": 1755700603776, "duration": 20909}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9df0629aeb726d588255055629526bcb"}, {"name": "test_next_channel", "children": [{"name": "测试next channel能正常执行", "uid": "7487eccd0b883f61", "parentUid": "3f23899abed5acee33c72c68b61ec080", "status": "passed", "time": {"start": 1755700618459, "stop": 1755700639682, "duration": 21223}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f23899abed5acee33c72c68b61ec080"}, {"name": "test_open_camera", "children": [{"name": "测试open camera能正常执行", "uid": "96e71235bdda612e", "parentUid": "470a45e0cfa4244680b09ac2a8c782a2", "status": "passed", "time": {"start": 1755700654244, "stop": 1755700684707, "duration": 30463}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "470a45e0cfa4244680b09ac2a8c782a2"}, {"name": "test_open_clock", "children": [{"name": "open clock", "uid": "6d7775be5b587cf2", "parentUid": "50daf9198779f130ca593a54f87ec128", "status": "failed", "time": {"start": 1755700702631, "stop": 1755700731127, "duration": 28496}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "50daf9198779f130ca593a54f87ec128"}, {"name": "test_open_contact", "children": [{"name": "测试open contact命令", "uid": "99484f3a117fde9b", "parentUid": "c18b5d7a76803510c6e0d2868a096cc6", "status": "passed", "time": {"start": 1755700746451, "stop": 1755700783074, "duration": 36623}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c18b5d7a76803510c6e0d2868a096cc6"}, {"name": "test_open_countdown", "children": [{"name": "测试open countdown能正常执行", "uid": "9834e78b2b998f9d", "parentUid": "34dd669acbf251ecc3b4bcb8039abeb9", "status": "passed", "time": {"start": 1755700798110, "stop": 1755700819231, "duration": 21121}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34dd669acbf251ecc3b4bcb8039abeb9"}, {"name": "test_open_dialer", "children": [{"name": "测试open dialer能正常执行", "uid": "1a1358239fa47472", "parentUid": "9072ee3e27d0b6c6a2866a718c2df078", "status": "passed", "time": {"start": 1755700833480, "stop": 1755700866973, "duration": 33493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9072ee3e27d0b6c6a2866a718c2df078"}, {"name": "test_open_ella", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "88e8a16f703da5cb", "parentUid": "4ee54c4b3bb105e867eb3473949bd494", "status": "passed", "time": {"start": 1755700881604, "stop": 1755700903197, "duration": 21593}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4ee54c4b3bb105e867eb3473949bd494"}, {"name": "test_open_folax", "children": [{"name": "测试open folax能正常执行", "uid": "3c5b657eb5c56f22", "parentUid": "e02fc6e8f5b7a4fa147ce01474445b5b", "status": "passed", "time": {"start": 1755700917970, "stop": 1755700940067, "duration": 22097}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e02fc6e8f5b7a4fa147ce01474445b5b"}, {"name": "test_open_phone", "children": [{"name": "测试open contact命令", "uid": "d358252686d0a9f1", "parentUid": "342f26d794168c8355629898d89335fe", "status": "passed", "time": {"start": 1755700954911, "stop": 1755700988454, "duration": 33543}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "342f26d794168c8355629898d89335fe"}, {"name": "test_pause_fm", "children": [{"name": "测试pause fm能正常执行", "uid": "99fa151c33c67f6d", "parentUid": "83a06f2be77f3b44f6a099f43b575dbf", "status": "passed", "time": {"start": 1755701003039, "stop": 1755701023560, "duration": 20521}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83a06f2be77f3b44f6a099f43b575dbf"}, {"name": "test_pause_music", "children": [{"name": "测试pause music能正常执行", "uid": "79a7c43986e8c28a", "parentUid": "a3026ab8ba7d276b771465a73b8cd69f", "status": "passed", "time": {"start": 1755701037928, "stop": 1755701059246, "duration": 21318}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a3026ab8ba7d276b771465a73b8cd69f"}, {"name": "test_pause_song", "children": [{"name": "测试pause song能正常执行", "uid": "119856ab8c08f6bd", "parentUid": "1a65a9bcb8b35f4b071220b80ad32958", "status": "passed", "time": {"start": 1755701074036, "stop": 1755701095411, "duration": 21375}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1a65a9bcb8b35f4b071220b80ad32958"}, {"name": "test_phone_boost", "children": [{"name": "测试phone boost能正常执行", "uid": "e218cc5b5c759e28", "parentUid": "e9cea1e20f3779ed164d139ee8b570ab", "status": "passed", "time": {"start": 1755701110195, "stop": 1755701131153, "duration": 20958}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e9cea1e20f3779ed164d139ee8b570ab"}, {"name": "test_play_afro_strut", "children": [{"name": "测试play afro strut", "uid": "8d0be30952efafa5", "parentUid": "51e1e1cd02cc1154b480c322b15c41c7", "status": "passed", "time": {"start": 1755701145485, "stop": 1755701184603, "duration": 39118}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "51e1e1cd02cc1154b480c322b15c41c7"}, {"name": "test_play_jay_chou_s_music", "children": [{"name": "测试play jay chou's music", "uid": "5b1d5fbfac869bd8", "parentUid": "80ffd53a52b22963552b5b4f8ad4a69c", "status": "passed", "time": {"start": 1755701199251, "stop": 1755701232104, "duration": 32853}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80ffd53a52b22963552b5b4f8ad4a69c"}, {"name": "test_play_jay_chou_s_music_by_spotify", "children": [{"name": "测试play jay chou's music by spotify", "uid": "fe7678a6c04e5be5", "parentUid": "fb3c6fdc98f3f31c0cad30d7f977b25b", "status": "failed", "time": {"start": 1755701246247, "stop": 1755701269209, "duration": 22962}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb3c6fdc98f3f31c0cad30d7f977b25b"}, {"name": "test_play_music", "children": [{"name": "测试play music", "uid": "b735d7161e2ebf8b", "parentUid": "012cce9a22125b4f9274376f463a21a6", "status": "passed", "time": {"start": 1755701283744, "stop": 1755701319743, "duration": 35999}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "012cce9a22125b4f9274376f463a21a6"}, {"name": "test_play_rock_music", "children": [{"name": "测试play rock music", "uid": "f79d0f1fb4940a1c", "parentUid": "5fe073acfe44a1d8a05ea050b2df638f", "status": "passed", "time": {"start": 1755701334357, "stop": 1755701366862, "duration": 32505}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5fe073acfe44a1d8a05ea050b2df638f"}, {"name": "test_play_sun_be_song_of_jide_chord", "children": [{"name": "测试play sun be song of jide chord", "uid": "478e1e1d32621f8c", "parentUid": "e61c3229af1d310b3286353485e7dc26", "status": "passed", "time": {"start": 1755701381405, "stop": 1755701413575, "duration": 32170}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e61c3229af1d310b3286353485e7dc26"}, {"name": "test_previous_music", "children": [{"name": "测试previous music能正常执行", "uid": "e07ae7b684220b6a", "parentUid": "a7e1f637551ed2330d6d67ed54a71f33", "status": "passed", "time": {"start": 1755701427896, "stop": 1755701448480, "duration": 20584}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a7e1f637551ed2330d6d67ed54a71f33"}, {"name": "test_record_audio_for_seconds", "children": [{"name": "测试record audio for 5 seconds能正常执行", "uid": "ee35e6bf1fc33f7e", "parentUid": "cfa8b6a3fd06497697d78e23fab251dd", "status": "failed", "time": {"start": 1755701462783, "stop": 1755701489976, "duration": 27193}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cfa8b6a3fd06497697d78e23fab251dd"}, {"name": "test_resume_music", "children": [{"name": "测试resume music能正常执行", "uid": "73d9c98de41b4a4c", "parentUid": "0f680c0e3c91eaed54cda04d37208ed3", "status": "passed", "time": {"start": 1755701504496, "stop": 1755701524938, "duration": 20442}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0f680c0e3c91eaed54cda04d37208ed3"}, {"name": "test_set_an_alarm_at_8_am", "children": [{"name": "测试set an alarm at 8 am", "uid": "fc372c49af1edc60", "parentUid": "934efa6c18aa0ed520853ffbd4ef3bd7", "status": "passed", "time": {"start": 1755701539602, "stop": 1755701560876, "duration": 21274}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "934efa6c18aa0ed520853ffbd4ef3bd7"}, {"name": "test_stop_playing", "children": [{"name": "测试stop playing", "uid": "6fef0ab2837f6386", "parentUid": "c598fac3e058ad96b70d8acf40dc13d8", "status": "passed", "time": {"start": 1755701575688, "stop": 1755701597710, "duration": 22022}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c598fac3e058ad96b70d8acf40dc13d8"}, {"name": "test_take_a_screenshot", "children": [{"name": "测试take a screenshot能正常执行", "uid": "9abdc970d1b20950", "parentUid": "c1793294d28b52937b4f0aa9a0fee952", "status": "passed", "time": {"start": 1755701612443, "stop": 1755701635846, "duration": 23403}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c1793294d28b52937b4f0aa9a0fee952"}, {"name": "test_turn_off_the_7_am_alarm", "children": [{"name": "测试turn off the 7AM alarm", "uid": "4c061d340532979d", "parentUid": "32bfb55cdaddbf6d8fd789b810c908d9", "status": "passed", "time": {"start": 1755701650372, "stop": 1755701670821, "duration": 20449}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "32bfb55cdaddbf6d8fd789b810c908d9"}, {"name": "test_turn_off_the_8_am_alarm", "children": [{"name": "测试turn off the 8 am alarm", "uid": "d5ec8af845715ba7", "parentUid": "568593413cf95986c665f08593ea21e9", "status": "passed", "time": {"start": 1755701685197, "stop": 1755701705762, "duration": 20565}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "568593413cf95986c665f08593ea21e9"}, {"name": "test_turn_on_the_alarm_at_8_am", "children": [{"name": "测试turn on the alarm at 8 am", "uid": "450d5205bf19d570", "parentUid": "739e8257af5cd9a36c4e8e1ddaad5c24", "status": "passed", "time": {"start": 1755701720522, "stop": 1755701742753, "duration": 22231}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "739e8257af5cd9a36c4e8e1ddaad5c24"}, {"name": "test_what_s_the_weather_like_in_shanghai_today", "children": [{"name": "测试What's the weather like in Shanghai today能正常执行", "uid": "50a9bd26888475a8", "parentUid": "85d39690a527a90b7d30ade2fdd4a8fc", "status": "passed", "time": {"start": 1755701757661, "stop": 1755701785974, "duration": 28313}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "85d39690a527a90b7d30ade2fdd4a8fc"}], "uid": "155057a0afc640ed94e8c8d7c444a680"}, {"name": "dialogue", "children": [{"name": "test_appeler_maman", "children": [{"name": "测试appeler maman能正常执行", "uid": "89017fbed8e16c62", "parentUid": "7954f72724ef95cff1433b0efbee5646", "status": "passed", "time": {"start": 1755701800427, "stop": 1755701820824, "duration": 20397}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7954f72724ef95cff1433b0efbee5646"}, {"name": "test_book_a_flight_to_paris", "children": [{"name": "测试book a flight to paris返回正确的不支持响应", "uid": "b0a48258d05596ff", "parentUid": "8f0bd634d6e7676a3a49d6dff49497fa", "status": "broken", "time": {"start": 1755701835293, "stop": 1755701855687, "duration": 20394}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8f0bd634d6e7676a3a49d6dff49497fa"}, {"name": "test_call_mom_through_whatsapp", "children": [{"name": "测试call mom through whatsapp能正常执行", "uid": "4cc7766d33a1432f", "parentUid": "8541ffcf150af606538b232e3aea7434", "status": "passed", "time": {"start": 1755701870600, "stop": 1755701900365, "duration": 29765}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8541ffcf150af606538b232e3aea7434"}, {"name": "test_can_you_give_me_a_coin", "children": [{"name": "测试can you give me a coin能正常执行", "uid": "e43d803ea41dae27", "parentUid": "7e22a70303a1dcd81523a4e5a786a8c4", "status": "passed", "time": {"start": 1755701914884, "stop": 1755701938613, "duration": 23729}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7e22a70303a1dcd81523a4e5a786a8c4"}, {"name": "test_cannot_login_in_google_email_box", "children": [{"name": "测试cannot login in google email box能正常执行", "uid": "a3530bbd2b584699", "parentUid": "8c0c4d699c270397d14ca2a01dc1ee04", "status": "failed", "time": {"start": 1755701953332, "stop": 1755701973811, "duration": 20479}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c0c4d699c270397d14ca2a01dc1ee04"}, {"name": "test_check_status_updates_on_whatsapp", "children": [{"name": "测试check status updates on whatsapp能正常执行", "uid": "5fd4997f0e350915", "parentUid": "4ea0f2069633d579cb2aa7e3f4dee562", "status": "passed", "time": {"start": 1755701988813, "stop": 1755702011621, "duration": 22808}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4ea0f2069633d579cb2aa7e3f4dee562"}, {"name": "test_close_whatsapp", "children": [{"name": "测试close whatsapp能正常执行", "uid": "a70e7d90c6f3b074", "parentUid": "254be578575e3bd49816bef9d50f165f", "status": "passed", "time": {"start": 1755702025810, "stop": 1755702048461, "duration": 22651}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "254be578575e3bd49816bef9d50f165f"}, {"name": "test_continue_playing", "children": [{"name": "测试continue playing能正常执行", "uid": "8b9194a09068301f", "parentUid": "d1d509a5f33ff7082393c4843543ffff", "status": "passed", "time": {"start": 1755702062873, "stop": 1755702085196, "duration": 22323}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d1d509a5f33ff7082393c4843543ffff"}, {"name": "test_could_you_please_search_an_for_me", "children": [{"name": "测试could you please search an for me能正常执行", "uid": "ed3602d899fe4932", "parentUid": "7327508f376f094b8e8fcbe0310288ab", "status": "failed", "time": {"start": 1755702099861, "stop": 1755702121075, "duration": 21214}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7327508f376f094b8e8fcbe0310288ab"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "测试disable magic voice changer能正常执行", "uid": "8a42e411053541d3", "parentUid": "3622231925f5d7d87dfc0a1200f98b66", "status": "passed", "time": {"start": 1755702135941, "stop": 1755702156660, "duration": 20719}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3622231925f5d7d87dfc0a1200f98b66"}, {"name": "test_give_me_some_money", "children": [{"name": "测试give me some money能正常执行", "uid": "5d999b8da9f2eecc", "parentUid": "690fab1bc7060b99c06d337a10d44d74", "status": "passed", "time": {"start": 1755702171420, "stop": 1755702194995, "duration": 23575}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "690fab1bc7060b99c06d337a10d44d74"}, {"name": "test_global_gdp_trends", "children": [{"name": "测试global gdp trends能正常执行", "uid": "83e930097c463f5", "parentUid": "76c541180f5aa46cc8bcc707c7d9c16f", "status": "passed", "time": {"start": 1755702209601, "stop": 1755702232800, "duration": 23199}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "76c541180f5aa46cc8bcc707c7d9c16f"}, {"name": "test_go_on_playing_fm", "children": [{"name": "测试go on playing fm能正常执行", "uid": "9fa336ffb418ce25", "parentUid": "f0bcda9b9eeb890ffe415969c037c977", "status": "passed", "time": {"start": 1755702247237, "stop": 1755702269458, "duration": 22221}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f0bcda9b9eeb890ffe415969c037c977"}, {"name": "test_hello_hello", "children": [{"name": "测试hello hello能正常执行", "uid": "a6a2d00c2b278d2", "parentUid": "b9ee9e8a38234ec69db09f78700d7fbb", "status": "passed", "time": {"start": 1755702283509, "stop": 1755702305954, "duration": 22445}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b9ee9e8a38234ec69db09f78700d7fbb"}, {"name": "test_help_me_write_an_email_to_make_an_appointment_for_a_visit", "children": [{"name": "测试Help me write an email to make an appointment for a visit能正常执行", "uid": "3219738aacaddb47", "parentUid": "c54c88063ac1edf2e569991ff95d16e9", "status": "passed", "time": {"start": 1755702320625, "stop": 1755702344247, "duration": 23622}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c54c88063ac1edf2e569991ff95d16e9"}, {"name": "test_hi", "children": [{"name": "测试hi能正常执行", "uid": "68c83f240cb84ef1", "parentUid": "acfbc4c8f3f5adee7b09787ceaa2dd0d", "status": "passed", "time": {"start": 1755702359010, "stop": 1755702381566, "duration": 22556}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "acfbc4c8f3f5adee7b09787ceaa2dd0d"}, {"name": "test_how_is_the_weather_today", "children": [{"name": "测试how is the weather today能正常执行", "uid": "8bce5d4f21a14189", "parentUid": "35fc4938c7d094c99682c376792faffe", "status": "failed", "time": {"start": 1755702396253, "stop": 1755702422756, "duration": 26503}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "35fc4938c7d094c99682c376792faffe"}, {"name": "test_how_is_the_wheather_today", "children": [{"name": "测试how is the wheather today能正常执行", "uid": "e33e43fb5c8a4fcf", "parentUid": "ccd640573d805b985ffb9441df6e5fe3", "status": "failed", "time": {"start": 1755702437492, "stop": 1755702458309, "duration": 20817}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ccd640573d805b985ffb9441df6e5fe3"}, {"name": "test_how_s_the_weather_today", "children": [{"name": "测试how's the weather today?返回正确的不支持响应", "uid": "d41160b7175fd6cf", "parentUid": "addae1e5b6cb9e45cb30b52b0519d0df", "status": "failed", "time": {"start": 1755702473412, "stop": 1755702500832, "duration": 27420}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "addae1e5b6cb9e45cb30b52b0519d0df"}, {"name": "test_how_s_the_weather_today_in_shanghai", "children": [{"name": "测试how's the weather today in shanghai能正常执行", "uid": "caa814f725fc2a01", "parentUid": "5cbfc9081ff73a6bd530b2831a9e3384", "status": "passed", "time": {"start": 1755702515608, "stop": 1755702543010, "duration": 27402}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cbfc9081ff73a6bd530b2831a9e3384"}, {"name": "test_how_to_say_hello_in_french", "children": [{"name": "测试how to say hello in french能正常执行", "uid": "8d93d331a494628b", "parentUid": "58f878e12ecf6b0d82d8bb49999f4d47", "status": "passed", "time": {"start": 1755702557725, "stop": 1755702579666, "duration": 21941}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "58f878e12ecf6b0d82d8bb49999f4d47"}, {"name": "test_how_to_say_i_love_you_in_french", "children": [{"name": "测试how to say i love you in french能正常执行", "uid": "3688f63a779e5a30", "parentUid": "82fbe34286468477e9134feb14dde382", "status": "passed", "time": {"start": 1755702594293, "stop": 1755702615786, "duration": 21493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "82fbe34286468477e9134feb14dde382"}, {"name": "test_i_wanna_be_rich", "children": [{"name": "测试i wanna be rich能正常执行", "uid": "137e5c8f5f4436f9", "parentUid": "e8c194f64f4bc6de5e59e9c7b048cfe4", "status": "passed", "time": {"start": 1755702630224, "stop": 1755702653824, "duration": 23600}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e8c194f64f4bc6de5e59e9c7b048cfe4"}, {"name": "test_i_want_to_listen_to_fm", "children": [{"name": "测试i want to listen to fm能正常执行", "uid": "cee60a7f7bc35e19", "parentUid": "62c1ecd6121de1385415edcb14e97d2c", "status": "passed", "time": {"start": 1755702668144, "stop": 1755702688954, "duration": 20810}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "62c1ecd6121de1385415edcb14e97d2c"}, {"name": "test_i_want_to_make_a_call", "children": [{"name": "测试i want to make a call能正常执行", "uid": "f94209c151cc2013", "parentUid": "3a5af3715ffe87f7125f4fc9d4a39a79", "status": "passed", "time": {"start": 1755702703335, "stop": 1755702733979, "duration": 30644}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3a5af3715ffe87f7125f4fc9d4a39a79"}, {"name": "test_i_want_to_watch_fireworks", "children": [{"name": "测试i want to watch fireworks能正常执行", "uid": "283291484f57bc21", "parentUid": "2d9bf69b53882c59bc26f9d5bf23b4c7", "status": "passed", "time": {"start": 1755702748126, "stop": 1755702771669, "duration": 23543}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d9bf69b53882c59bc26f9d5bf23b4c7"}, {"name": "test_introduce_yourself", "children": [{"name": "测试introduce yourself能正常执行", "uid": "bab75f16bde0ccde", "parentUid": "472715b25ea329acf3f408d9a469274a", "status": "passed", "time": {"start": 1755702786033, "stop": 1755702808456, "duration": 22423}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "472715b25ea329acf3f408d9a469274a"}, {"name": "test_last_channel", "children": [{"name": "测试last channel能正常执行", "uid": "18015d1175ce4542", "parentUid": "1673f8e5a00bba040872744c10466a1a", "status": "passed", "time": {"start": 1755702823288, "stop": 1755702844554, "duration": 21266}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1673f8e5a00bba040872744c10466a1a"}, {"name": "test_listen_to_fm", "children": [{"name": "测试listen to fm能正常执行", "uid": "f2b00b9e13a3df07", "parentUid": "3f8cc40b215a2f65db4811e729bc65fd", "status": "passed", "time": {"start": 1755702859443, "stop": 1755702879903, "duration": 20460}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3f8cc40b215a2f65db4811e729bc65fd"}, {"name": "test_make_a_call", "children": [{"name": "测试make a call能正常执行", "uid": "85e50e395982bf38", "parentUid": "11ee179329ce36ecafe38100e52ec70d", "status": "passed", "time": {"start": 1755702894282, "stop": 1755702923163, "duration": 28881}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11ee179329ce36ecafe38100e52ec70d"}, {"name": "test_measure_blood_oxygen", "children": [{"name": "测试measure blood oxygen", "uid": "45f1052203b29d68", "parentUid": "2707f2dd651484f2df0cab1faf1ff403", "status": "passed", "time": {"start": 1755702937954, "stop": 1755702959235, "duration": 21281}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2707f2dd651484f2df0cab1faf1ff403"}, {"name": "test_measure_heart_rate", "children": [{"name": "测试measure heart rate", "uid": "68ed7aab547d2d9d", "parentUid": "df37b64c34a0bb9e92aee8469cf785c9", "status": "passed", "time": {"start": 1755702973812, "stop": 1755702994422, "duration": 20610}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df37b64c34a0bb9e92aee8469cf785c9"}, {"name": "test_next_music", "children": [{"name": "测试next music能正常执行", "uid": "7e0add9ec94ae39b", "parentUid": "23537c9370c1a822549b7a79eb682856", "status": "passed", "time": {"start": 1755703008961, "stop": 1755703029423, "duration": 20462}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "23537c9370c1a822549b7a79eb682856"}, {"name": "test_next_song", "children": [{"name": "测试next song能正常执行", "uid": "5a2a7f94ed174bd7", "parentUid": "036f43ecd5f7343046e2af56838f0b2c", "status": "passed", "time": {"start": 1755703043747, "stop": 1755703065191, "duration": 21444}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "036f43ecd5f7343046e2af56838f0b2c"}, {"name": "test_open_app", "children": [{"name": "测试open contact命令 - 简洁版本", "uid": "6df2e183af43c36b", "parentUid": "c969d4a520c25845a9e33a24d4e3b7a4", "status": "passed", "time": {"start": 1755703079746, "stop": 1755703100097, "duration": 20351}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c969d4a520c25845a9e33a24d4e3b7a4"}, {"name": "test_pause_music", "children": [{"name": "测试pause music能正常执行", "uid": "9b8438d7ed2a785e", "parentUid": "80d0cfc5ba53853ee4e5c374ac3ca7b4", "status": "passed", "time": {"start": 1755703114361, "stop": 1755703134975, "duration": 20614}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80d0cfc5ba53853ee4e5c374ac3ca7b4"}, {"name": "test_play_music_by_VLC", "children": [{"name": "测试play music by VLC", "uid": "2053024043889923", "parentUid": "0cffe8d8bb255395290a9dd513723b30", "status": "passed", "time": {"start": 1755703149714, "stop": 1755703173141, "duration": 23427}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0cffe8d8bb255395290a9dd513723b30"}, {"name": "test_play_music_by_boomplay", "children": [{"name": "测试play music by boomplay", "uid": "a0516cb668e579d5", "parentUid": "973892d18717ef61a9c60231ae0888fd", "status": "failed", "time": {"start": 1755703187627, "stop": 1755703210256, "duration": 22629}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "973892d18717ef61a9c60231ae0888fd"}, {"name": "test_play_music_by_visha", "children": [{"name": "测试play music by visha", "uid": "f83e9d3340e3be7e", "parentUid": "7b9d8d24ad1638d28448c5d76170b0db", "status": "failed", "time": {"start": 1755703224996, "stop": 1755703256431, "duration": 31435}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b9d8d24ad1638d28448c5d76170b0db"}, {"name": "test_play_music_by_yandex_music", "children": [{"name": "测试play music by yandex music", "uid": "e121af0e43898b5", "parentUid": "480e35527d7a53156164d862f3505bc3", "status": "passed", "time": {"start": 1755703271698, "stop": 1755703294462, "duration": 22764}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "480e35527d7a53156164d862f3505bc3"}, {"name": "test_play_music_on_boomplayer", "children": [{"name": "测试play music on boomplayer", "uid": "58903762c7912ce0", "parentUid": "b9e3972a06a525f9a243e4a5ad6d8284", "status": "failed", "time": {"start": 1755703309148, "stop": 1755703332570, "duration": 23422}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b9e3972a06a525f9a243e4a5ad6d8284"}, {"name": "test_play_music_on_visha", "children": [{"name": "测试play music on visha", "uid": "9303e393f4bece4c", "parentUid": "45b5c17a13f2f2c58c18e0c5403b8174", "status": "passed", "time": {"start": 1755703347335, "stop": 1755703379238, "duration": 31903}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "45b5c17a13f2f2c58c18e0c5403b8174"}, {"name": "test_play_news", "children": [{"name": "测试play news", "uid": "e2ca15307ddb2dc0", "parentUid": "0545a1fc4a946cb1c690d92255face5c", "status": "passed", "time": {"start": 1755703393400, "stop": 1755703416211, "duration": 22811}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0545a1fc4a946cb1c690d92255face5c"}, {"name": "test_play_political_news", "children": [{"name": "测试play political news", "uid": "24e9abe1ba38f937", "parentUid": "350bcef11f4fb6eb99f97d3fa6fe1d47", "status": "passed", "time": {"start": 1755703430671, "stop": 1755703454457, "duration": 23786}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "350bcef11f4fb6eb99f97d3fa6fe1d47"}, {"name": "test_previous_song", "children": [{"name": "测试previous song能正常执行", "uid": "1dc62f6cb6eaeb3a", "parentUid": "4277679ed8d0b781135c825e64cd8c91", "status": "passed", "time": {"start": 1755703469271, "stop": 1755703489648, "duration": 20377}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4277679ed8d0b781135c825e64cd8c91"}, {"name": "test_remove_alarms", "children": [{"name": "测试remove alarms能正常执行", "uid": "19f2756da55e22b9", "parentUid": "a37f4999e35eb21b851cb519de2c19b5", "status": "passed", "time": {"start": 1755703503992, "stop": 1755703525473, "duration": 21481}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a37f4999e35eb21b851cb519de2c19b5"}, {"name": "test_say_hello", "children": [{"name": "测试say hello能正常执行", "uid": "4f4c41d20a93d13f", "parentUid": "b695b344ff456dc284b52017667bcd48", "status": "passed", "time": {"start": 1755703540319, "stop": 1755703563881, "duration": 23562}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b695b344ff456dc284b52017667bcd48"}, {"name": "test_search_my_gallery_for_food_pictures", "children": [{"name": "测试search my gallery for food pictures能正常执行", "uid": "4696dac000e64935", "parentUid": "cdb79778de81160b7231a87afe6bd1ba", "status": "passed", "time": {"start": 1755703578511, "stop": 1755703603431, "duration": 24920}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cdb79778de81160b7231a87afe6bd1ba"}, {"name": "test_search_picture_in_my_gallery", "children": [{"name": "测试search picture in my gallery能正常执行", "uid": "7f92b55039421904", "parentUid": "2ee81b68d6fe8782f4c16078e5ded646", "status": "passed", "time": {"start": 1755703617920, "stop": 1755703643356, "duration": 25436}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2ee81b68d6fe8782f4c16078e5ded646"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "测试searching for a method of violent murder能正常执行", "uid": "684ccf6e53e263d3", "parentUid": "f3e7c7fb3655fd1be27c6d251a6f492c", "status": "broken", "time": {"start": 1755703657746, "stop": 1755703677263, "duration": 19517}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f3e7c7fb3655fd1be27c6d251a6f492c"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "测试send my recent photos to mom through whatsapp能正常执行", "uid": "4f291caf47ab1d3b", "parentUid": "11264fcbfd285641cac64cf3c0f1f4a8", "status": "passed", "time": {"start": 1755703692385, "stop": 1755703716666, "duration": 24281}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11264fcbfd285641cac64cf3c0f1f4a8"}, {"name": "test_show_me_premier_le<PERSON><PERSON>_goal_ranking", "children": [{"name": "测试show me premier leaguage goal ranking能正常执行", "uid": "c2bccb74d1e66bcd", "parentUid": "51407e55f461bb75e0ea1d0bcd974a5d", "status": "passed", "time": {"start": 1755703731070, "stop": 1755703753253, "duration": 22183}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "51407e55f461bb75e0ea1d0bcd974a5d"}, {"name": "test_show_my_all_alarms", "children": [{"name": "测试show my all alarms能正常执行", "uid": "9cda2cd83df5f4d2", "parentUid": "7d58aebf2bec28bf77ceb0d213b422ee", "status": "passed", "time": {"start": 1755703768001, "stop": 1755703789007, "duration": 21006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7d58aebf2bec28bf77ceb0d213b422ee"}, {"name": "test_show_scores_between_livepool_and_manchester_city", "children": [{"name": "测试show scores between livepool and manchester city能正常执行", "uid": "acd10b2f8b72c4b2", "parentUid": "390af3b968f43ff01d77f8b7960b3916", "status": "passed", "time": {"start": 1755703803433, "stop": 1755703825132, "duration": 21699}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "390af3b968f43ff01d77f8b7960b3916"}, {"name": "test_start_countdown", "children": [{"name": "测试start countdown能正常执行", "uid": "9535fcce1508ad8", "parentUid": "7f509a668a017ed18e7c0a0484490f1d", "status": "passed", "time": {"start": 1755703839947, "stop": 1755703860715, "duration": 20768}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7f509a668a017ed18e7c0a0484490f1d"}, {"name": "test_stop_music", "children": [{"name": "测试stop music能正常执行", "uid": "cac94fb9e31f53e8", "parentUid": "77eaeb306e34a7a9bfb4cedeba8ee35f", "status": "passed", "time": {"start": 1755703875261, "stop": 1755703896293, "duration": 21032}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "77eaeb306e34a7a9bfb4cedeba8ee35f"}, {"name": "test_stop_run", "children": [{"name": "测试stop run能正常执行", "uid": "df7783d41ebacf14", "parentUid": "ccf9f3b4d1f905064aacc7d3127597b9", "status": "passed", "time": {"start": 1755703910928, "stop": 1755703931783, "duration": 20855}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ccf9f3b4d1f905064aacc7d3127597b9"}, {"name": "test_stop_workout", "children": [{"name": "测试stop workout能正常执行", "uid": "6268808e2335924f", "parentUid": "8ee49369bb9d601da7f062855654c904", "status": "passed", "time": {"start": 1755703945988, "stop": 1755703966624, "duration": 20636}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8ee49369bb9d601da7f062855654c904"}, {"name": "test_summarize_content_on_this_page", "children": [{"name": "测试summarize content on this page能正常执行", "uid": "c5d972e9ac233338", "parentUid": "b6a6c5c1cc3bbfd99be0b798596d90fd", "status": "passed", "time": {"start": 1755703981243, "stop": 1755704001914, "duration": 20671}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b6a6c5c1cc3bbfd99be0b798596d90fd"}, {"name": "test_take_a_joke", "children": [{"name": "测试take a joke能正常执行", "uid": "159b858661233ae7", "parentUid": "a1543d72151cd8524884aba6ef4613f6", "status": "passed", "time": {"start": 1755704016473, "stop": 1755704038350, "duration": 21877}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a1543d72151cd8524884aba6ef4613f6"}, {"name": "test_take_a_note_on_how_to_build_a_treehouse", "children": [{"name": "测试take a note on how to build a treehouse能正常执行", "uid": "e0b1e46089f66afe", "parentUid": "218955f9fce08124722f8aa63ee5293a", "status": "passed", "time": {"start": 1755704053355, "stop": 1755704074002, "duration": 20647}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "218955f9fce08124722f8aa63ee5293a"}, {"name": "test_take_notes_on_how_to_build_a_treehouse", "children": [{"name": "测试take notes on how to build a treehouse能正常执行", "uid": "3602fab948853dc9", "parentUid": "acd0d4c0ff78ae721b07111ac720141b", "status": "passed", "time": {"start": 1755704088367, "stop": 1755704113810, "duration": 25443}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "acd0d4c0ff78ae721b07111ac720141b"}, {"name": "test_tell_me_a_joke", "children": [{"name": "测试tell me a joke能正常执行", "uid": "595d2d67a593ff6f", "parentUid": "1efd42262e16a36d4ac595eed9b7e185", "status": "failed", "time": {"start": 1755704128497, "stop": 1755704149109, "duration": 20612}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1efd42262e16a36d4ac595eed9b7e185"}, {"name": "test_unset_alarms", "children": [{"name": "测试unset alarms能正常执行", "uid": "e2e4fd5ef808353d", "parentUid": "8eb34f4ad03f2b3301605936af5f6db6", "status": "passed", "time": {"start": 1755704163804, "stop": 1755704184666, "duration": 20862}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8eb34f4ad03f2b3301605936af5f6db6"}, {"name": "test_video_call_mom_through_whatsapp", "children": [{"name": "测试video call mom through whatsapp能正常执行", "uid": "45fadb3dac70f843", "parentUid": "f8daa61044336f258fd904820f097662", "status": "failed", "time": {"start": 1755704199431, "stop": 1755704227984, "duration": 28553}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f8daa61044336f258fd904820f097662"}, {"name": "test_view_recent_alarms", "children": [{"name": "测试view recent alarms能正常执行", "uid": "fb2c209e944023e8", "parentUid": "42362c26e7ec72bda0a34e7ccd060ca6", "status": "passed", "time": {"start": 1755704242504, "stop": 1755704263386, "duration": 20882}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "42362c26e7ec72bda0a34e7ccd060ca6"}, {"name": "test_what_is_apec", "children": [{"name": "测试what is apec?能正常执行", "uid": "ba45865cf853620b", "parentUid": "4567e57227f42cc7993892d86e523dbf", "status": "passed", "time": {"start": 1755704278135, "stop": 1755704300338, "duration": 22203}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4567e57227f42cc7993892d86e523dbf"}, {"name": "test_what_languages_do_you_support", "children": [{"name": "测试What languages do you support能正常执行", "uid": "d9e2a42bc660c8a2", "parentUid": "20b964271968b9aba9b13a138825e688", "status": "passed", "time": {"start": 1755704315163, "stop": 1755704335969, "duration": 20806}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20b964271968b9aba9b13a138825e688"}, {"name": "test_what_s_the_weather_like_today", "children": [{"name": "测试What's the weather like today能正常执行", "uid": "5fb1f8cdacb21403", "parentUid": "51399e7e2539b443ff346f3e8db98136", "status": "failed", "time": {"start": 1755704350521, "stop": 1755704378244, "duration": 27723}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "51399e7e2539b443ff346f3e8db98136"}, {"name": "test_what_s_the_weather_today", "children": [{"name": "测试what·s the weather today？能正常执行", "uid": "2ef45ceda14a7624", "parentUid": "af2f763310dd868fb7293de462763b64", "status": "failed", "time": {"start": 1755704393144, "stop": 1755704419726, "duration": 26582}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "af2f763310dd868fb7293de462763b64"}, {"name": "test_what_s_the_wheather_today", "children": [{"name": "测试what's the wheather today?能正常执行", "uid": "afe67ecef8a98cc0", "parentUid": "b880b898b5ae6eb8576adc4f20cdd8c6", "status": "failed", "time": {"start": 1755704434485, "stop": 1755704454493, "duration": 20008}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b880b898b5ae6eb8576adc4f20cdd8c6"}, {"name": "test_what_s_your_name", "children": [{"name": "测试what's your name？能正常执行", "uid": "6992a6d49c5017f9", "parentUid": "4b64931505303134ebf07e49f3a9c0af", "status": "passed", "time": {"start": 1755704469574, "stop": 1755704490346, "duration": 20772}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4b64931505303134ebf07e49f3a9c0af"}, {"name": "test_what_time_is_it_now", "children": [{"name": "测试what time is it now能正常执行", "uid": "fc2a1d7492dfd853", "parentUid": "6a367b30c7c610464ef1a8acfa0f93c9", "status": "passed", "time": {"start": 1755704504441, "stop": 1755704526816, "duration": 22375}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6a367b30c7c610464ef1a8acfa0f93c9"}, {"name": "test_whats_the_weather_today", "children": [{"name": "测试what's the weather today?能正常执行", "uid": "5a47ad7486e4ec29", "parentUid": "d38e79c83442fa3e23fba8b6ba34de56", "status": "failed", "time": {"start": 1755704541401, "stop": 1755704569475, "duration": 28074}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d38e79c83442fa3e23fba8b6ba34de56"}, {"name": "test_who_is_harry_potter", "children": [{"name": "测试who is harry potter能正常执行", "uid": "51c00d45fa6af6db", "parentUid": "651e0dc624a6f14649ebc34a90907bc1", "status": "passed", "time": {"start": 1755704584240, "stop": 1755704605401, "duration": 21161}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "651e0dc624a6f14649ebc34a90907bc1"}, {"name": "test_who_is_j_k_rowling", "children": [{"name": "测试who is j k rowling能正常执行", "uid": "c33639a362546746", "parentUid": "9e3d19482db247c568f3393a2db09f1f", "status": "passed", "time": {"start": 1755704619903, "stop": 1755704641600, "duration": 21697}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e3d19482db247c568f3393a2db09f1f"}, {"name": "test_why_is_my_phone_not_ringing_on_incoming_calls", "children": [{"name": "测试why is my phone not ringing on incoming calls能正常执行", "uid": "f77e3b68b75abd44", "parentUid": "d08c8b89f0cacddcb2f61563717a9da6", "status": "passed", "time": {"start": 1755704656348, "stop": 1755704685142, "duration": 28794}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d08c8b89f0cacddcb2f61563717a9da6"}, {"name": "test_why_my_charging_is_so_slow", "children": [{"name": "测试why my charging is so slow能正常执行", "uid": "180455e93fc454c4", "parentUid": "015300f55fc1d98de4057611dffc7dd0", "status": "failed", "time": {"start": 1755704699610, "stop": 1755704722347, "duration": 22737}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "015300f55fc1d98de4057611dffc7dd0"}], "uid": "9c28642eff039d4ac586a58d4f4a0368"}, {"name": "self_function", "children": [{"name": "test_a_cute_little_boy_is_skiing", "children": [{"name": "测试A cute little boy is skiing 能正常执行", "uid": "70e614510c75c93b", "parentUid": "5dce92d62b80cb6553f1512dba0fb4c1", "status": "passed", "time": {"start": 1755704737411, "stop": 1755704814836, "duration": 77425}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5dce92d62b80cb6553f1512dba0fb4c1"}, {"name": "test_change_the_style_of_this_image_to_d_cartoon", "children": [{"name": "测试Change the style of this image to 3D cartoon能正常执行", "uid": "f4c408a5aabd00f2", "parentUid": "df3998e3ff6eea9977d2eb96f3dafe5c", "status": "passed", "time": {"start": 1755704829532, "stop": 1755704941072, "duration": 111540}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df3998e3ff6eea9977d2eb96f3dafe5c"}, {"name": "test_document_summary", "children": [{"name": "测试document summary能正常执行", "uid": "e3ecb8cc47bce00f", "parentUid": "c53eeb531d41bb17ebbb8c4a5a1c0c7f", "status": "failed", "time": {"start": 1755704955804, "stop": 1755705043062, "duration": 87258}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c53eeb531d41bb17ebbb8c4a5a1c0c7f"}, {"name": "test_extend_the_image", "children": [{"name": "测试extend the image能正常执行", "uid": "516f66c98dd46d2", "parentUid": "424f36be71517de9612f5c1c4bbfd51b", "status": "passed", "time": {"start": 1755705058382, "stop": 1755705174540, "duration": 116158}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "424f36be71517de9612f5c1c4bbfd51b"}, {"name": "test_puppy", "children": [{"name": "测试puppy能正常执行", "uid": "35be0a29733330f", "parentUid": "5c4a74895e8f5f371499f67901869735", "status": "passed", "time": {"start": 1755705189398, "stop": 1755705266614, "duration": 77216}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5c4a74895e8f5f371499f67901869735"}, {"name": "test_scan_the_qr_code_in_the_image", "children": [{"name": "测试Scan the QR code in the image 能正常执行", "uid": "b5b43cb03a6debaa", "parentUid": "6d5e42acbf3b14ff9c7a1cb039af1eca", "status": "passed", "time": {"start": 1755705281263, "stop": 1755705393269, "duration": 112006}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6d5e42acbf3b14ff9c7a1cb039af1eca"}, {"name": "test_scan_this_qr_code", "children": [{"name": "测试Scan this QR code 能正常执行", "uid": "d353a8306eb76825", "parentUid": "e69f8752d4c2fff88f531d31725048e7", "status": "passed", "time": {"start": 1755705407528, "stop": 1755705518704, "duration": 111176}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e69f8752d4c2fff88f531d31725048e7"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "测试Summarize what I'm reading能正常执行", "uid": "57d7096266b039f9", "parentUid": "4a719e0465c60ccc21a482f8a248c354", "status": "failed", "time": {"start": 1755705533332, "stop": 1755705583011, "duration": 49679}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a719e0465c60ccc21a482f8a248c354"}], "uid": "d4441fc536166659c8ba844cb229b96d"}, {"name": "system_coupling", "children": [{"name": "test_adjustment_the_brightness_to", "children": [{"name": "测试Adjustment the brightness to 50%能正常执行", "uid": "339ac4fc9f765983", "parentUid": "f42ec0f584493c7e8f49de225d028d41", "status": "passed", "time": {"start": 1755705598498, "stop": 1755705619924, "duration": 21426}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f42ec0f584493c7e8f49de225d028d41"}, {"name": "test_adjustment_the_brightness_to_maximun", "children": [{"name": "测试adjustment the brightness to maximun能正常执行", "uid": "a96fd6fef86d423", "parentUid": "0a124f1e35221e2f7fc58638efac4609", "status": "passed", "time": {"start": 1755705634423, "stop": 1755705655282, "duration": 20859}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0a124f1e35221e2f7fc58638efac4609"}, {"name": "test_adjustment_the_brightness_to_minimun", "children": [{"name": "测试adjustment the brightness to minimun能正常执行", "uid": "3afa7a07e2667b4b", "parentUid": "6cabd7a2575b3a49ba1d7e27cbebac2c", "status": "passed", "time": {"start": 1755705670212, "stop": 1755705691592, "duration": 21380}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6cabd7a2575b3a49ba1d7e27cbebac2c"}, {"name": "test_boost_phone", "children": [{"name": "测试boost phone能正常执行", "uid": "34918f91ba99a1ce", "parentUid": "084463cd25c5bdf79a97950326707013", "status": "passed", "time": {"start": 1755705705901, "stop": 1755705726833, "duration": 20932}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "084463cd25c5bdf79a97950326707013"}, {"name": "test_change_your_language", "children": [{"name": "测试change your language能正常执行", "uid": "45df33e0046924ed", "parentUid": "6e01a0998eaccde4524ec4e2c73e42c4", "status": "passed", "time": {"start": 1755705741223, "stop": 1755705765810, "duration": 24587}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e01a0998eaccde4524ec4e2c73e42c4"}, {"name": "test_change_your_language_to_chinese", "children": [{"name": "测试change your language to chinese能正常执行", "uid": "4105641adf8eeed4", "parentUid": "3ac391fa66b27ea8ccce7735db22cb6a", "status": "skipped", "time": {"start": 1755705767182, "stop": 1755705767182, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "@pytest.mark.skip(reason='语言设置为中文，影响别的Case，先跳过')"]}], "uid": "3ac391fa66b27ea8ccce7735db22cb6a"}, {"name": "test_check_front_camera_information", "children": [{"name": "测试check front camera information能正常执行", "uid": "bfe2edf9ead1e168", "parentUid": "a3c3dc73a42e85d88737ae9cfea06ccf", "status": "passed", "time": {"start": 1755705780394, "stop": 1755705804190, "duration": 23796}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a3c3dc73a42e85d88737ae9cfea06ccf"}, {"name": "test_clear_junk_files", "children": [{"name": "测试clear junk files命令", "uid": "c8fa830383111da9", "parentUid": "79b6df9a290803cad2cc59c26f69f0d5", "status": "passed", "time": {"start": 1755705818835, "stop": 1755705851636, "duration": 32801}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79b6df9a290803cad2cc59c26f69f0d5"}, {"name": "test_close_airplane", "children": [{"name": "测试close airplane能正常执行", "uid": "eb70cf2137fe243f", "parentUid": "472d53b48cd9911b61f6571aa3fabc81", "status": "passed", "time": {"start": 1755705865868, "stop": 1755705886037, "duration": 20169}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "472d53b48cd9911b61f6571aa3fabc81"}, {"name": "test_close_bluetooth", "children": [{"name": "测试close bluetooth能正常执行", "uid": "5524ec83adbefa12", "parentUid": "43c3e77fe7845ae372f8b29b479e9c36", "status": "passed", "time": {"start": 1755705900799, "stop": 1755705923362, "duration": 22563}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43c3e77fe7845ae372f8b29b479e9c36"}, {"name": "test_close_flashlight", "children": [{"name": "测试close flashlight能正常执行", "uid": "1e30d8eaa155c870", "parentUid": "05e7de1f6a30400f4ba09b6b927c0c5f", "status": "passed", "time": {"start": 1755705938584, "stop": 1755705961241, "duration": 22657}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "05e7de1f6a30400f4ba09b6b927c0c5f"}, {"name": "test_close_wifi", "children": [{"name": "测试close wifi能正常执行", "uid": "d3ccb51a7e3071bf", "parentUid": "2fcb64cb23f984daec672237d94bb890", "status": "skipped", "time": {"start": 1755705962643, "stop": 1755705962643, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='该脚本较特殊，先跳过')", "smoke"]}], "uid": "2fcb64cb23f984daec672237d94bb890"}, {"name": "test_countdown_min", "children": [{"name": "测试countdown 5 min能正常执行", "uid": "db51eea29e85759", "parentUid": "5acc0b94de2365f9212477ba5756e434", "status": "failed", "time": {"start": 1755705975718, "stop": 1755706004279, "duration": 28561}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5acc0b94de2365f9212477ba5756e434"}, {"name": "test_decrease_the_brightness", "children": [{"name": "测试decrease the brightness能正常执行", "uid": "bf0190e9cd5a8476", "parentUid": "71d6a5aa3a46022e7d7ccf02f396978b", "status": "failed", "time": {"start": 1755706019510, "stop": 1755706040717, "duration": 21207}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "71d6a5aa3a46022e7d7ccf02f396978b"}, {"name": "test_decrease_the_volume_to_the_minimun", "children": [{"name": "测试decrease the volume to the minimun能正常执行", "uid": "99ec4f33dc304231", "parentUid": "20521c4eded335a33ef2e1e8ba13e750", "status": "passed", "time": {"start": 1755706055513, "stop": 1755706078043, "duration": 22530}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20521c4eded335a33ef2e1e8ba13e750"}, {"name": "test_end_screen_recording", "children": [{"name": "stop  screen recording能正常执行", "uid": "5e31517853f4fdb7", "parentUid": "62823fc182e440a2772cd17c42ab4c29", "status": "failed", "time": {"start": 1755706093068, "stop": 1755706142874, "duration": 49806}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "62823fc182e440a2772cd17c42ab4c29"}, {"name": "test_help_me_take_a_long_screenshot", "children": [{"name": "测试help me take a long screenshot能正常执行", "uid": "84bf7ebf445383bc", "parentUid": "1349bd48475843b043511c26d6b12b24", "status": "passed", "time": {"start": 1755706158440, "stop": 1755706182603, "duration": 24163}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1349bd48475843b043511c26d6b12b24"}, {"name": "test_help_me_take_a_screenshot", "children": [{"name": "测试help me take a screenshot能正常执行", "uid": "2ef78000037a1acc", "parentUid": "f5c6c4880b5d8ab718c388b911880945", "status": "passed", "time": {"start": 1755706197680, "stop": 1755706221962, "duration": 24282}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f5c6c4880b5d8ab718c388b911880945"}, {"name": "test_increase_notification_volume", "children": [{"name": "测试increase notification volume能正常执行", "uid": "fa0d98e48edee82d", "parentUid": "c820439150f9f3eeda53e313099e370f", "status": "passed", "time": {"start": 1755706236836, "stop": 1755706258231, "duration": 21395}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c820439150f9f3eeda53e313099e370f"}, {"name": "test_increase_screen_brightness", "children": [{"name": "测试increase screen brightness能正常执行", "uid": "e20f716311878e59", "parentUid": "c07e39e268fcc0b13f001cda95d292f1", "status": "passed", "time": {"start": 1755706273406, "stop": 1755706295159, "duration": 21753}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c07e39e268fcc0b13f001cda95d292f1"}, {"name": "test_increase_the_brightness", "children": [{"name": "测试increase the brightness能正常执行", "uid": "ac0cf8db9f9152f6", "parentUid": "318cb9a601f399198ad7b64303d6a804", "status": "passed", "time": {"start": 1755706309995, "stop": 1755706331340, "duration": 21345}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "318cb9a601f399198ad7b64303d6a804"}, {"name": "test_increase_the_volume_to_the_maximun", "children": [{"name": "测试increase the volume to the maximun能正常执行", "uid": "a0cc9190d48976f5", "parentUid": "6daa3caef60cbff270b232fec14c929c", "status": "passed", "time": {"start": 1755706345675, "stop": 1755706366929, "duration": 21254}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6daa3caef60cbff270b232fec14c929c"}, {"name": "test_long_screenshot", "children": [{"name": "测试long screenshot能正常执行", "uid": "2e87037a25b53754", "parentUid": "eee53365e608da87e8c1a38cd7dffd82", "status": "passed", "time": {"start": 1755706381481, "stop": 1755706404917, "duration": 23436}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eee53365e608da87e8c1a38cd7dffd82"}, {"name": "test_make_the_phone_mute", "children": [{"name": "测试make the phone mute能正常执行", "uid": "97eb7e4115d75c48", "parentUid": "0b44ef42d6ad53a2bea3bbed424e41ee", "status": "passed", "time": {"start": 1755706419084, "stop": 1755706440513, "duration": 21429}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0b44ef42d6ad53a2bea3bbed424e41ee"}, {"name": "test_max_alarm_clock_volume", "children": [{"name": "测试max alarm clock volume", "uid": "fbbff9436bd5a1d0", "parentUid": "cb770bbd2e94c64f600ababeb4f0ee6a", "status": "passed", "time": {"start": 1755706455372, "stop": 1755706476586, "duration": 21214}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cb770bbd2e94c64f600ababeb4f0ee6a"}, {"name": "test_max_brightness", "children": [{"name": "测试max brightness能正常执行", "uid": "b732fc1153a563f6", "parentUid": "bc6c7f74ec9c5d95879c2d1cf9880a19", "status": "passed", "time": {"start": 1755706491479, "stop": 1755706513206, "duration": 21727}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bc6c7f74ec9c5d95879c2d1cf9880a19"}, {"name": "test_max_notifications_volume", "children": [{"name": "测试max notifications volume能正常执行", "uid": "f9845e8245082057", "parentUid": "d168a4bb52fda427e53b993ad648e85a", "status": "passed", "time": {"start": 1755706527526, "stop": 1755706549367, "duration": 21841}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d168a4bb52fda427e53b993ad648e85a"}, {"name": "test_max_ring_volume", "children": [{"name": "测试max ring volume能正常执行", "uid": "2847dfe35b9b9f1a", "parentUid": "446c602b84cd3dd1f96b31e0108411fb", "status": "passed", "time": {"start": 1755706563961, "stop": 1755706585204, "duration": 21243}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "446c602b84cd3dd1f96b31e0108411fb"}, {"name": "test_maximum_volume", "children": [{"name": "测试maximum volume能正常执行", "uid": "c9b4155386854041", "parentUid": "df87e0d39fb14600720a434db404fb7b", "status": "passed", "time": {"start": 1755706599479, "stop": 1755706621212, "duration": 21733}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df87e0d39fb14600720a434db404fb7b"}, {"name": "test_memory_cleanup", "children": [{"name": "测试memory cleanup能正常执行", "uid": "52deae9f27e577b1", "parentUid": "3060b8cc7f41dc69110dfc534e6b31d5", "status": "passed", "time": {"start": 1755706636094, "stop": 1755706685028, "duration": 48934}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3060b8cc7f41dc69110dfc534e6b31d5"}, {"name": "test_min_alarm_clock_volume", "children": [{"name": "测试min alarm clock volume", "uid": "1298311829448811", "parentUid": "fd1b7fb10ddbe5eadf93766446821b65", "status": "passed", "time": {"start": 1755706699791, "stop": 1755706720799, "duration": 21008}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fd1b7fb10ddbe5eadf93766446821b65"}, {"name": "test_min_brightness", "children": [{"name": "测试min brightness能正常执行", "uid": "857baf7b1c9bdf69", "parentUid": "e5eb14b295c00a0d59407203918f4795", "status": "passed", "time": {"start": 1755706735522, "stop": 1755706757517, "duration": 21995}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e5eb14b295c00a0d59407203918f4795"}, {"name": "test_min_notifications_volume", "children": [{"name": "测试min notifications volume能正常执行", "uid": "c8796ec65c91d6b6", "parentUid": "11646edd82fe620b37c2b64152ced5c6", "status": "passed", "time": {"start": 1755706772154, "stop": 1755706793603, "duration": 21449}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "11646edd82fe620b37c2b64152ced5c6"}, {"name": "test_min_ring_volume", "children": [{"name": "测试min ring volume能正常执行", "uid": "13888369295144ee", "parentUid": "d45b602df43d160bc186bdce526932e8", "status": "passed", "time": {"start": 1755706808156, "stop": 1755706829809, "duration": 21653}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d45b602df43d160bc186bdce526932e8"}, {"name": "test_minimum_volume", "children": [{"name": "测试minimum volume能正常执行", "uid": "7c7a84e12488462f", "parentUid": "25a58f43bfaf0da153ec687fea5dcbb5", "status": "passed", "time": {"start": 1755706844303, "stop": 1755706866562, "duration": 22259}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "25a58f43bfaf0da153ec687fea5dcbb5"}, {"name": "test_open_bluetooth", "children": [{"name": "测试open bluetooth", "uid": "2641a32928086e2f", "parentUid": "31e546db33350801c6eaef968b45b6aa", "status": "passed", "time": {"start": 1755706881299, "stop": 1755706903925, "duration": 22626}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "31e546db33350801c6eaef968b45b6aa"}, {"name": "test_open_bt", "children": [{"name": "测试open bt", "uid": "6c5f86b548af54ce", "parentUid": "176a0fdfd9c4144b8dfe3d66a049fda5", "status": "passed", "time": {"start": 1755706918545, "stop": 1755706939645, "duration": 21100}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "176a0fdfd9c4144b8dfe3d66a049fda5"}, {"name": "test_open_flashlight", "children": [{"name": "测试open flashlight", "uid": "260c2eeb61aaf9d7", "parentUid": "649e9c0b88c706f3f0551679bed64e42", "status": "passed", "time": {"start": 1755706954201, "stop": 1755706977150, "duration": 22949}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "649e9c0b88c706f3f0551679bed64e42"}, {"name": "test_open_wifi", "children": [{"name": "测试open wifi", "uid": "23f3c3c2daaeec8", "parentUid": "f9eadf40e8c6f09400b67a9959223d07", "status": "passed", "time": {"start": 1755706991973, "stop": 1755707013080, "duration": 21107}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f9eadf40e8c6f09400b67a9959223d07"}, {"name": "test_power_off", "children": [{"name": "测试power off能正常执行", "uid": "d0a6623db05a7ecf", "parentUid": "cc3a6f764485169c77c1128d52c6bffb", "status": "skipped", "time": {"start": 1755707014398, "stop": 1755707014398, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='power off 会导致设备断开，先跳过')", "smoke"]}], "uid": "cc3a6f764485169c77c1128d52c6bffb"}, {"name": "test_power_saving", "children": [{"name": "测试power saving能正常执行", "uid": "ca8badb071247c64", "parentUid": "c06c413949d25e13cefc3d7381223a9b", "status": "passed", "time": {"start": 1755707027099, "stop": 1755707055915, "duration": 28816}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c06c413949d25e13cefc3d7381223a9b"}, {"name": "test_screen_record", "children": [{"name": "测试screen record能正常执行", "uid": "b304259457fa7774", "parentUid": "b54fab3751e243d1b424e37d5d287ec3", "status": "passed", "time": {"start": 1755707070491, "stop": 1755707098024, "duration": 27533}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "ad3c5fc9da25816b", "parentUid": "b54fab3751e243d1b424e37d5d287ec3", "status": "failed", "time": {"start": 1755707112931, "stop": 1755707133620, "duration": 20689}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b54fab3751e243d1b424e37d5d287ec3"}, {"name": "test_set_a_timer_for_minutes", "children": [{"name": "测试set a timer for 10 minutes能正常执行", "uid": "90eedf7238194c12", "parentUid": "b51793332bcfdc1c2f251c5ba2c434e5", "status": "passed", "time": {"start": 1755707148485, "stop": 1755707177983, "duration": 29498}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b51793332bcfdc1c2f251c5ba2c434e5"}, {"name": "test_set_alarm_for_10_o_clock", "children": [{"name": "测试set alarm for 10 o'clock", "uid": "845234b003a4566f", "parentUid": "afeb5aed32d36d5eaa4b132ea5b382e0", "status": "failed", "time": {"start": 1755707193006, "stop": 1755707214799, "duration": 21793}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "afeb5aed32d36d5eaa4b132ea5b382e0"}, {"name": "test_set_alarm_volume", "children": [{"name": "测试set alarm volume 50", "uid": "6b2b66f9a390a323", "parentUid": "1c45deff54cbb6d416ba43bacd27945b", "status": "passed", "time": {"start": 1755707230071, "stop": 1755707251658, "duration": 21587}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1c45deff54cbb6d416ba43bacd27945b"}, {"name": "test_set_battery_saver_setting", "children": [{"name": "测试set Battery Saver setting能正常执行", "uid": "d4b28db88d96b053", "parentUid": "b6e60ab86df5fa2a1124b5d765cbfe9a", "status": "passed", "time": {"start": 1755707266109, "stop": 1755707298386, "duration": 32277}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b6e60ab86df5fa2a1124b5d765cbfe9a"}, {"name": "test_set_my_alarm_volume_to", "children": [{"name": "测试set my alarm volume to 50%", "uid": "9e482f7900f6f786", "parentUid": "bba654dbbf79ef15853a19abfddc76e4", "status": "passed", "time": {"start": 1755707313355, "stop": 1755707335982, "duration": 22627}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bba654dbbf79ef15853a19abfddc76e4"}, {"name": "test_set_notifications_volume_to", "children": [{"name": "测试set notifications volume to 50能正常执行", "uid": "46bca85c836641b0", "parentUid": "8941ad1b28523da85fde9e91215c0464", "status": "passed", "time": {"start": 1755707350893, "stop": 1755707372785, "duration": 21892}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8941ad1b28523da85fde9e91215c0464"}, {"name": "test_set_ringtone_volume_to", "children": [{"name": "测试set ringtone volume to 50能正常执行", "uid": "2d1447d297a62942", "parentUid": "9dd83889897a00276a8e889caae40a1f", "status": "passed", "time": {"start": 1755707387423, "stop": 1755707409716, "duration": 22293}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9dd83889897a00276a8e889caae40a1f"}, {"name": "test_set_screen_to_maximum_brightness", "children": [{"name": "测试set screen to maximum brightness能正常执行", "uid": "a9d7364ec53b988c", "parentUid": "545283340cc920a78362ea8849374231", "status": "passed", "time": {"start": 1755707424478, "stop": 1755707446436, "duration": 21958}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "545283340cc920a78362ea8849374231"}, {"name": "test_set_the_alarm_at_9_o_clock_on_weekends", "children": [{"name": "测试set the alarm at 9 o'clock on weekends", "uid": "500f2d3edf3668e6", "parentUid": "3921f68f918e595aa1edf4f965392437", "status": "passed", "time": {"start": 1755707461475, "stop": 1755707482773, "duration": 21298}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3921f68f918e595aa1edf4f965392437"}, {"name": "test_smart_charge", "children": [{"name": "测试smart charge能正常执行", "uid": "c4b327dbe29a37ce", "parentUid": "38c8329119fb1c04106bbcd94b91ce11", "status": "failed", "time": {"start": 1755707497944, "stop": 1755707518821, "duration": 20877}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "38c8329119fb1c04106bbcd94b91ce11"}, {"name": "test_start_record", "children": [{"name": "测试start record能正常执行", "uid": "f44afbc0139dc9a7", "parentUid": "9018ab08ec79118ab3c7ac811af9f4fa", "status": "passed", "time": {"start": 1755707533841, "stop": 1755707583695, "duration": 49854}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9018ab08ec79118ab3c7ac811af9f4fa"}, {"name": "test_start_screen_recording", "children": [{"name": "测试start screen recording能正常执行", "uid": "c5528074071c3cf3", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "passed", "time": {"start": 1755707598522, "stop": 1755707625184, "duration": 26662}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试pause screen recording能正常执行", "uid": "c41a5facdaf6e16", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "failed", "time": {"start": 1755707639972, "stop": 1755707660359, "duration": 20387}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "continue  screen recording能正常执行", "uid": "d60c330ba8cd6757", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "failed", "time": {"start": 1755707675377, "stop": 1755707696328, "duration": 20951}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "11f0b98ab62a7ad1", "parentUid": "a083bce7f3fa2d36f16068ac034f4e62", "status": "failed", "time": {"start": 1755707711049, "stop": 1755707731566, "duration": 20517}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a083bce7f3fa2d36f16068ac034f4e62"}, {"name": "test_stop_recording", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "d535b9830d965510", "parentUid": "4eb0eb9df164551aeca11800ab680c92", "status": "passed", "time": {"start": 1755707746600, "stop": 1755707773529, "duration": 26929}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "stop  screen recording能正常执行", "uid": "25d621e86d10808b", "parentUid": "4eb0eb9df164551aeca11800ab680c92", "status": "failed", "time": {"start": 1755707788346, "stop": 1755707809791, "duration": 21445}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4eb0eb9df164551aeca11800ab680c92"}, {"name": "test_switch_charging_modes", "children": [{"name": "测试switch charging modes能正常执行", "uid": "f84d3f6caf47f216", "parentUid": "43ff861df9a59eeef7ac3edd50740e02", "status": "failed", "time": {"start": 1755707824470, "stop": 1755707844851, "duration": 20381}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "43ff861df9a59eeef7ac3edd50740e02"}, {"name": "test_switch_magic_voice_to_grace", "children": [{"name": "测试Switch Magic Voice to Grace能正常执行", "uid": "5ddbc58a02a7ab5c", "parentUid": "035194a70f8a76a4b76a28f704b8ff10", "status": "passed", "time": {"start": 1755707859515, "stop": 1755707880347, "duration": 20832}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "035194a70f8a76a4b76a28f704b8ff10"}, {"name": "test_switch_magic_voice_to_mango", "children": [{"name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "uid": "dd4f0642dceeb932", "parentUid": "661f1bc7755dcb73f23369637ff67460", "status": "passed", "time": {"start": 1755707895286, "stop": 1755707915999, "duration": 20713}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "661f1bc7755dcb73f23369637ff67460"}, {"name": "test_switch_to_barrage_notification", "children": [{"name": "测试Switch to Barrage Notification能正常执行", "uid": "b9a2b43807e7424c", "parentUid": "71d9b05a5d33eaee0a7365eb0716032e", "status": "passed", "time": {"start": 1755707930297, "stop": 1755707950934, "duration": 20637}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "71d9b05a5d33eaee0a7365eb0716032e"}, {"name": "test_switch_to_default_mode", "children": [{"name": "测试switch to default mode能正常执行", "uid": "346a5de850ee3524", "parentUid": "62ed32eba114769fb134a432054912e4", "status": "passed", "time": {"start": 1755707965233, "stop": 1755707996393, "duration": 31160}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "62ed32eba114769fb134a432054912e4"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "测试switch to equilibrium mode能正常执行", "uid": "203fe3bef4728bf3", "parentUid": "2d94b30b5f27ad8b3f8967a2a3fdebe3", "status": "passed", "time": {"start": 1755708011086, "stop": 1755708032167, "duration": 21081}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d94b30b5f27ad8b3f8967a2a3fdebe3"}, {"name": "test_switch_to_flash_notification", "children": [{"name": "测试switch to flash notification能正常执行", "uid": "c0d0be5b36a40ca4", "parentUid": "561ddfab014b4de5926c248cf15d4c27", "status": "failed", "time": {"start": 1755708046384, "stop": 1755708077156, "duration": 30772}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "561ddfab014b4de5926c248cf15d4c27"}, {"name": "test_switch_to_hyper_charge", "children": [{"name": "测试Switch to Hyper Charge能正常执行", "uid": "c23bf9ba92d12972", "parentUid": "99e29771f7571a2fc146c069d7b2c125", "status": "failed", "time": {"start": 1755708092009, "stop": 1755708112333, "duration": 20324}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "99e29771f7571a2fc146c069d7b2c125"}, {"name": "test_switch_to_low_temp_charge", "children": [{"name": "测试Switch to Low-Temp Charge能正常执行", "uid": "b041df3626f271c3", "parentUid": "e6a1e2156d8852f07854b84613f04720", "status": "failed", "time": {"start": 1755708127204, "stop": 1755708147758, "duration": 20554}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e6a1e2156d8852f07854b84613f04720"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "测试switch to power saving mode能正常执行", "uid": "3c2e0c6fd2ccf328", "parentUid": "dd9e8f3389e8ba0ae9839c873338d760", "status": "passed", "time": {"start": 1755708162654, "stop": 1755708183285, "duration": 20631}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dd9e8f3389e8ba0ae9839c873338d760"}, {"name": "test_switch_to_smart_charge", "children": [{"name": "测试switch to smart charge能正常执行", "uid": "73fe2b51030a055c", "parentUid": "a22da6d101545dc9b0d8edfda3746b20", "status": "failed", "time": {"start": 1755708197642, "stop": 1755708217597, "duration": 19955}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a22da6d101545dc9b0d8edfda3746b20"}, {"name": "test_switched_to_data_mode", "children": [{"name": "测试switched to data mode能正常执行", "uid": "c2f61d128b461d27", "parentUid": "2902f5b25b15010f9670892ff424d92c", "status": "passed", "time": {"start": 1755708232186, "stop": 1755708253599, "duration": 21413}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2902f5b25b15010f9670892ff424d92c"}, {"name": "test_take_a_photo", "children": [{"name": "测试take a photo能正常执行", "uid": "796644c4e7b3e78e", "parentUid": "07997292671f0c2fc7c4f6639faf371f", "status": "failed", "time": {"start": 1755708268047, "stop": 1755708311973, "duration": 43926}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "07997292671f0c2fc7c4f6639faf371f"}, {"name": "test_take_a_selfie", "children": [{"name": "测试take a selfie能正常执行", "uid": "ce467c19240b201f", "parentUid": "b76edbc7be133088ff6cc7dcf79d0688", "status": "passed", "time": {"start": 1755708330448, "stop": 1755708372133, "duration": 41685}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b76edbc7be133088ff6cc7dcf79d0688"}, {"name": "test_the_battery_of_the_mobile_phone_is_too_low", "children": [{"name": "测试the battery of the mobile phone is too low能正常执行", "uid": "fbcb39b15b93ef8d", "parentUid": "fd16a15b2bee6fa00e607a69966a816d", "status": "passed", "time": {"start": 1755708391096, "stop": 1755708418442, "duration": 27346}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fd16a15b2bee6fa00e607a69966a816d"}, {"name": "test_turn_down_alarm_clock_volume", "children": [{"name": "测试turn down alarm clock volume", "uid": "c18c29d4fbf8459a", "parentUid": "058fbd49b611700b8e525883b7a715c7", "status": "passed", "time": {"start": 1755708433297, "stop": 1755708454918, "duration": 21621}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "058fbd49b611700b8e525883b7a715c7"}, {"name": "test_turn_down_notifications_volume", "children": [{"name": "测试turn down notifications volume能正常执行", "uid": "ffcc91edf810c005", "parentUid": "653ec2baabc9caa267ed836aab8220ec", "status": "failed", "time": {"start": 1755708469483, "stop": 1755708490778, "duration": 21295}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "653ec2baabc9caa267ed836aab8220ec"}, {"name": "test_turn_down_ring_volume", "children": [{"name": "测试turn down ring volume能正常执行", "uid": "f86949e3bb15b41f", "parentUid": "550486b451765b696814b7b6743f77fe", "status": "passed", "time": {"start": 1755708505469, "stop": 1755708526824, "duration": 21355}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "550486b451765b696814b7b6743f77fe"}, {"name": "test_turn_down_the_brightness_to_the_min", "children": [{"name": "测试turn down the brightness to the min能正常执行", "uid": "901e910a93d74ccd", "parentUid": "4a960850c7bf239ac31ab236d66e878f", "status": "passed", "time": {"start": 1755708541586, "stop": 1755708564338, "duration": 22752}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a960850c7bf239ac31ab236d66e878f"}, {"name": "test_turn_off_adaptive_brightness", "children": [{"name": "测试turn off adaptive brightness能正常执行", "uid": "96fb5e3e2559766c", "parentUid": "3e70ee736f216554340491db70fd8257", "status": "passed", "time": {"start": 1755708579016, "stop": 1755708599998, "duration": 20982}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e70ee736f216554340491db70fd8257"}, {"name": "test_turn_off_auto_rotate_screen", "children": [{"name": "测试turn off auto rotate screen能正常执行", "uid": "4ee5197beb31c8d2", "parentUid": "9029516e3ad0598210caeb84ca36028a", "status": "passed", "time": {"start": 1755708614679, "stop": 1755708635097, "duration": 20418}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9029516e3ad0598210caeb84ca36028a"}, {"name": "test_turn_off_flashlight", "children": [{"name": "测试turn off flashlight能正常执行", "uid": "3725a642eca617b0", "parentUid": "990086735fb4bd184a8ac5a25a5f821c", "status": "passed", "time": {"start": 1755708649746, "stop": 1755708672277, "duration": 22531}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "990086735fb4bd184a8ac5a25a5f821c"}, {"name": "test_turn_off_light_theme", "children": [{"name": "测试turn off light theme能正常执行", "uid": "abd7bf1438f0a7d2", "parentUid": "83d0ab198b941cca60896fe1448789bd", "status": "passed", "time": {"start": 1755708686834, "stop": 1755708707905, "duration": 21071}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "83d0ab198b941cca60896fe1448789bd"}, {"name": "test_turn_off_nfc", "children": [{"name": "测试turn off nfc能正常执行", "uid": "827822ecc26ee170", "parentUid": "25370c24404047b47256854554ecf19e", "status": "passed", "time": {"start": 1755708722461, "stop": 1755708745247, "duration": 22786}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "25370c24404047b47256854554ecf19e"}, {"name": "test_turn_off_smart_reminder", "children": [{"name": "测试turn off smart reminder能正常执行", "uid": "2db03739558319f1", "parentUid": "69969f8163b3af4121192a7c63c3fa49", "status": "passed", "time": {"start": 1755708760066, "stop": 1755708780737, "duration": 20671}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "69969f8163b3af4121192a7c63c3fa49"}, {"name": "test_turn_on_adaptive_brightness", "children": [{"name": "测试turn on adaptive brightness能正常执行", "uid": "929d72935693324b", "parentUid": "8074f6de3cc4d8e0ee673e8436e36098", "status": "failed", "time": {"start": 1755708795199, "stop": 1755708815144, "duration": 19945}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8074f6de3cc4d8e0ee673e8436e36098"}, {"name": "test_turn_on_airplane_mode", "children": [{"name": "测试turn on airplane mode能正常执行", "uid": "1fcee6f5e5d630ca", "parentUid": "01abd5dec02dc63c8d78a8b97b6ef4b1", "status": "skipped", "time": {"start": 1755708816729, "stop": 1755708816729, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "@pytest.mark.skip(reason='airplane mode 会导致设备断开网络，先跳过')"]}], "uid": "01abd5dec02dc63c8d78a8b97b6ef4b1"}, {"name": "test_turn_on_auto_rotate_screen", "children": [{"name": "测试turn on auto rotate screen能正常执行", "uid": "34d823de203fd775", "parentUid": "ae898b5cc3303c318290953f962dbbd3", "status": "failed", "time": {"start": 1755708829957, "stop": 1755708850102, "duration": 20145}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ae898b5cc3303c318290953f962dbbd3"}, {"name": "test_turn_on_bluetooth", "children": [{"name": "测试turn on bluetooth能正常执行", "uid": "18890841c4c2c4ce", "parentUid": "06858c4ec997c86c877ce6ab09e3bbaf", "status": "passed", "time": {"start": 1755708864308, "stop": 1755708885607, "duration": 21299}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "06858c4ec997c86c877ce6ab09e3bbaf"}, {"name": "test_turn_on_brightness_to_80", "children": [{"name": "测试turn on brightness to 80能正常执行", "uid": "847ab3cbd7b2ed1e", "parentUid": "2fb18876082f3ff4b34b7ccd72bb7b28", "status": "passed", "time": {"start": 1755708900298, "stop": 1755708921057, "duration": 20759}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2fb18876082f3ff4b34b7ccd72bb7b28"}, {"name": "test_turn_on_do_not_disturb_mode", "children": [{"name": "测试turn on do not disturb mode能正常执行", "uid": "3ea651471ee18f9a", "parentUid": "869ae35c254b699074c4a890b2986c6d", "status": "passed", "time": {"start": 1755708935625, "stop": 1755708956205, "duration": 20580}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "869ae35c254b699074c4a890b2986c6d"}, {"name": "test_turn_on_light_theme", "children": [{"name": "测试turn on light theme能正常执行", "uid": "4b4ca06394093464", "parentUid": "9684df9c422f9cf3eef3d6f0ddd6607c", "status": "passed", "time": {"start": 1755708970871, "stop": 1755708991635, "duration": 20764}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}, {"name": "测试turn on light theme能正常执行", "uid": "738b70d304f66c90", "parentUid": "9684df9c422f9cf3eef3d6f0ddd6607c", "status": "passed", "time": {"start": 1755709006278, "stop": 1755709026560, "duration": 20282}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9684df9c422f9cf3eef3d6f0ddd6607c"}, {"name": "test_turn_on_location_services", "children": [{"name": "测试turn on location services能正常执行", "uid": "493aee631e1e1647", "parentUid": "13b991141a19f0fe4b82a01990c6a851", "status": "passed", "time": {"start": 1755709041383, "stop": 1755709061912, "duration": 20529}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "13b991141a19f0fe4b82a01990c6a851"}, {"name": "test_turn_on_nfc", "children": [{"name": "测试turn on nfc能正常执行", "uid": "a1f6a422ff2a1c03", "parentUid": "ac9c5a7d76d32a3f0024f1bbc4260940", "status": "passed", "time": {"start": 1755709076870, "stop": 1755709099324, "duration": 22454}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ac9c5a7d76d32a3f0024f1bbc4260940"}, {"name": "test_turn_on_smart_reminder", "children": [{"name": "测试turn on smart reminder能正常执行", "uid": "5bfde334bbb93da9", "parentUid": "2ea8c3d3e0404ec5eb56e144f27b3b76", "status": "failed", "time": {"start": 1755709113798, "stop": 1755709134251, "duration": 20453}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2ea8c3d3e0404ec5eb56e144f27b3b76"}, {"name": "test_turn_on_the_7am_alarm", "children": [{"name": "测试turn on the 7AM alarm", "uid": "dd6625e7baf2349d", "parentUid": "20bb02d2344b8a65effdb63f9de8d6cf", "status": "passed", "time": {"start": 1755709149214, "stop": 1755709170312, "duration": 21098}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "20bb02d2344b8a65effdb63f9de8d6cf"}, {"name": "test_turn_on_the_flashlight", "children": [{"name": "测试turn on the flashlight能正常执行", "uid": "70b4795c0bc7ede2", "parentUid": "c5e4b9b650952bb94f49d9ea64fb9376", "status": "passed", "time": {"start": 1755709184668, "stop": 1755709206538, "duration": 21870}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c5e4b9b650952bb94f49d9ea64fb9376"}, {"name": "test_turn_on_the_screen_record", "children": [{"name": "测试turn on the screen record能正常执行", "uid": "9e7da9e0fe709259", "parentUid": "5cf049f3605e39b97e2902b74b81323d", "status": "passed", "time": {"start": 1755709221263, "stop": 1755709269759, "duration": 48496}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cf049f3605e39b97e2902b74b81323d"}, {"name": "test_turn_on_wifi", "children": [{"name": "测试turn on wifi能正常执行", "uid": "df4ac9080a8fd69e", "parentUid": "8f8981d5b6534e5c462a0d8cbafb4302", "status": "passed", "time": {"start": 1755709284284, "stop": 1755709305126, "duration": 20842}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8f8981d5b6534e5c462a0d8cbafb4302"}, {"name": "test_turn_up_alarm_clock_volume", "children": [{"name": "测试turn up alarm clock volume", "uid": "dc18d3af30b26f69", "parentUid": "dcce4917ed7cc8e4416996e747de9c57", "status": "passed", "time": {"start": 1755709319712, "stop": 1755709340963, "duration": 21251}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dcce4917ed7cc8e4416996e747de9c57"}, {"name": "test_turn_up_notifications_volume", "children": [{"name": "测试turn up notifications volume能正常执行", "uid": "64169ad1b4b90399", "parentUid": "b0cf36b6141ae92300d37ba7f5cc28b4", "status": "passed", "time": {"start": 1755709355890, "stop": 1755709376884, "duration": 20994}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0cf36b6141ae92300d37ba7f5cc28b4"}, {"name": "test_turn_up_ring_volume", "children": [{"name": "测试turn up ring volume能正常执行", "uid": "a6f80c7773f88ea5", "parentUid": "d5f810cb9cfc767a77e9451e7e822fb9", "status": "passed", "time": {"start": 1755709391731, "stop": 1755709413674, "duration": 21943}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d5f810cb9cfc767a77e9451e7e822fb9"}, {"name": "test_turn_up_the_brightness_to_the_max", "children": [{"name": "测试turn up the brightness to the max能正常执行", "uid": "f64c738cc7a9bff6", "parentUid": "6357b07572538890d600f578a4c3c4aa", "status": "passed", "time": {"start": 1755709428263, "stop": 1755709448886, "duration": 20623}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6357b07572538890d600f578a4c3c4aa"}, {"name": "test_turn_up_the_volume_to_the_max", "children": [{"name": "测试turn up the volume to the max能正常执行", "uid": "3f6b89daaeb1bc0e", "parentUid": "2d41809a57eff7adaff72dd6fb331012", "status": "passed", "time": {"start": 1755709463485, "stop": 1755709484276, "duration": 20791}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d41809a57eff7adaff72dd6fb331012"}, {"name": "test_wake_me_up_at_am_tomorrow", "children": [{"name": "测试wake me up at 7:00 am tomorrow能正常执行", "uid": "71f530986a1c345", "parentUid": "193cbbfa30db6a41aacdad7a864e09bc", "status": "failed", "time": {"start": 1755709498977, "stop": 1755709519819, "duration": 20842}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "193cbbfa30db6a41aacdad7a864e09bc"}, {"name": "test_where_is_the_carlcare_service_outlet", "children": [{"name": "测试where is the carlcare service outlet能正常执行", "uid": "82733299fa7cd70e", "parentUid": "a69c8f4ca42c7f7233299fb00d064c9f", "status": "passed", "time": {"start": 1755709534776, "stop": 1755709560729, "duration": 25953}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a69c8f4ca42c7f7233299fb00d064c9f"}], "uid": "93fc5475dc5c7ad817f2b1ab5b0ac7b9"}, {"name": "third_coupling", "children": [{"name": "test_download_app", "children": [{"name": "测试download app能正常执行", "uid": "ba657cd04a6dab4c", "parentUid": "b132f6b6f7a47dbe39c1fb7de000fe78", "status": "passed", "time": {"start": 1755709575847, "stop": 1755709598382, "duration": 22535}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b132f6b6f7a47dbe39c1fb7de000fe78"}, {"name": "test_download_basketball", "children": [{"name": "测试download basketball能正常执行", "uid": "a48f4d786e1c8577", "parentUid": "9ad7d52518459e93bf395586c4de03d3", "status": "failed", "time": {"start": 1755709612923, "stop": 1755709634652, "duration": 21729}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9ad7d52518459e93bf395586c4de03d3"}, {"name": "test_download_qq", "children": [{"name": "测试download qq能正常执行", "uid": "cbd725d38ded5c9e", "parentUid": "88f428f9095db30aa10835e50fff2f44", "status": "passed", "time": {"start": 1755709649631, "stop": 1755709675727, "duration": 26096}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "88f428f9095db30aa10835e50fff2f44"}, {"name": "test_find_a_restaurant_near_me", "children": [{"name": "测试find a restaurant near me能正常执行", "uid": "2f8312ea7202df42", "parentUid": "990b9417ef5d8284b5ed1c8f1dcb2456", "status": "passed", "time": {"start": 1755709690853, "stop": 1755709721504, "duration": 30651}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "990b9417ef5d8284b5ed1c8f1dcb2456"}, {"name": "test_navigate_from_beijing_to_shanghai", "children": [{"name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "uid": "938d0ec59845c0f0", "parentUid": "98910e36f666a4645d039127f271b15c", "status": "passed", "time": {"start": 1755709735934, "stop": 1755709764303, "duration": 28369}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98910e36f666a4645d039127f271b15c"}, {"name": "test_navigate_from_to_red_square", "children": [{"name": "测试navigate from to red square能正常执行", "uid": "41a8d8e5a2de5632", "parentUid": "b5db47a2899bd2831280d03f6ab5ccde", "status": "passed", "time": {"start": 1755709779151, "stop": 1755709808495, "duration": 29344}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b5db47a2899bd2831280d03f6ab5ccde"}, {"name": "test_navigate_to_shanghai_disneyland", "children": [{"name": "测试navigate to shanghai disneyland能正常执行", "uid": "e468d6a321e1e20", "parentUid": "63d4dbd18e96d1791e9029023802405a", "status": "passed", "time": {"start": 1755709823196, "stop": 1755709851513, "duration": 28317}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "63d4dbd18e96d1791e9029023802405a"}, {"name": "test_navigation_to_the_lucky", "children": [{"name": "测试navigation to the lucky能正常执行", "uid": "eb3d8fcdee428cd0", "parentUid": "26d631af39b9512dace23d786ed426bc", "status": "passed", "time": {"start": 1755709865960, "stop": 1755709892497, "duration": 26537}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "26d631af39b9512dace23d786ed426bc"}, {"name": "test_open_facebook", "children": [{"name": "测试open facebook能正常执行", "uid": "aa022482314d4b36", "parentUid": "28034a46d61d0cc3fef6e9ffd4db7994", "status": "passed", "time": {"start": 1755709906929, "stop": 1755709935577, "duration": 28648}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "28034a46d61d0cc3fef6e9ffd4db7994"}, {"name": "test_open_whatsapp", "children": [{"name": "测试open whatsapp", "uid": "26d776b4fe794136", "parentUid": "300a56019ec17b2ba7bcf48ab420b3c3", "status": "failed", "time": {"start": 1755709949839, "stop": 1755709971745, "duration": 21906}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "300a56019ec17b2ba7bcf48ab420b3c3"}, {"name": "test_order_a_burger", "children": [{"name": "测试order a burger能正常执行", "uid": "8332a4b64b5294bb", "parentUid": "2f48f1343c12cc6c07d8e886b62e4c0d", "status": "failed", "time": {"start": 1755709986074, "stop": 1755710006183, "duration": 20109}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2f48f1343c12cc6c07d8e886b62e4c0d"}, {"name": "test_order_a_takeaway", "children": [{"name": "测试order a takeaway能正常执行", "uid": "24b291e1c219b3ab", "parentUid": "540441448375adc4953206d10f542ed2", "status": "failed", "time": {"start": 1755710021144, "stop": 1755710041268, "duration": 20124}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "540441448375adc4953206d10f542ed2"}, {"name": "test_pls_open_the_newest_whatsapp_activity", "children": [{"name": "测试pls open the newest whatsapp activity", "uid": "e277df3e3e307782", "parentUid": "18df90aff668e599fc314e80b0851b3d", "status": "passed", "time": {"start": 1755710056245, "stop": 1755710078932, "duration": 22687}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "18df90aff668e599fc314e80b0851b3d"}, {"name": "test_whatsapp", "children": [{"name": "测试whatsapp能正常执行", "uid": "b47ff2b67a74943e", "parentUid": "3936daaae04788e4f3494c8c7b4dd2c4", "status": "passed", "time": {"start": 1755710093623, "stop": 1755710116183, "duration": 22560}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3936daaae04788e4f3494c8c7b4dd2c4"}], "uid": "f10231acc09c45f9430bedaedd076cbf"}, {"name": "unsupported_commands", "children": [{"name": "test_Add_the_images_and_text_on_the_screen_to_the_note", "children": [{"name": "测试Add the images and text on the screen to the note", "uid": "f2bfad1ddd35325d", "parentUid": "b7d205f78a6f3e533fa2c219a8aae3b4", "status": "passed", "time": {"start": 1755710130878, "stop": 1755710151458, "duration": 20580}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b7d205f78a6f3e533fa2c219a8aae3b4"}, {"name": "test_Language_List", "children": [{"name": "测试Language List", "uid": "7a4f53635514e0ea", "parentUid": "acd49ea16884c92b4bc0827ff1a9b518", "status": "passed", "time": {"start": 1755710165942, "stop": 1755710186411, "duration": 20469}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "acd49ea16884c92b4bc0827ff1a9b518"}, {"name": "test_a_clear_and_pink_crystal_necklace_in_the_water", "children": [{"name": "测试a clear and pink crystal necklace in the water", "uid": "b07f145ff380d57a", "parentUid": "868b92d4860fe62d37a238a2a03ab514", "status": "passed", "time": {"start": 1755710200825, "stop": 1755710222912, "duration": 22087}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "868b92d4860fe62d37a238a2a03ab514"}, {"name": "test_a_clear_glass_cup", "children": [{"name": "测试a clear glass cup", "uid": "d0df3bdd8860ce09", "parentUid": "7c33673a4b0e7e0bb3ee397c5642873f", "status": "passed", "time": {"start": 1755710237336, "stop": 1755710258435, "duration": 21099}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c33673a4b0e7e0bb3ee397c5642873f"}, {"name": "test_a_cute_little_boy_is_skiing", "children": [{"name": "测试A cute little boy is skiing", "uid": "32cde91effcb28af", "parentUid": "39ee1e792baffbc9f8bd43921c2e8f29", "status": "passed", "time": {"start": 1755710272924, "stop": 1755710293558, "duration": 20634}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "39ee1e792baffbc9f8bd43921c2e8f29"}, {"name": "test_a_cute_little_girl_with_long_hair", "children": [{"name": "测试A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her", "uid": "46eda86c2db0307b", "parentUid": "b95f5c90bba17331cb7f1bfb4dc8e34c", "status": "failed", "time": {"start": 1755710307810, "stop": 1755710328714, "duration": 20904}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b95f5c90bba17331cb7f1bfb4dc8e34c"}, {"name": "test_a_furry_little_monkey", "children": [{"name": "测试A furry little monkey", "uid": "7abda32690218b48", "parentUid": "8edf4e99036a18e605daf4b2a5d6d006", "status": "passed", "time": {"start": 1755710343414, "stop": 1755710363990, "duration": 20576}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8edf4e99036a18e605daf4b2a5d6d006"}, {"name": "test_a_little_raccoon_is_walking_on_the_forest_meadow_surrounded_by_a_row_of_green_bamboo_groves_with_layers_of_high_mountains_shrouded_in_clouds_and_mist_in_the_distance", "children": [{"name": "测试A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance", "uid": "137d3ab7ca2b4635", "parentUid": "0b181376538184b1e498cc28c7d921b2", "status": "passed", "time": {"start": 1755710378620, "stop": 1755710401319, "duration": 22699}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0b181376538184b1e498cc28c7d921b2"}, {"name": "test_a_little_raccoon_walks_on_a_forest_meadow", "children": [{"name": "测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "uid": "2fb30ed5d8c97efd", "parentUid": "125ccc5d6c2cd29e3eff4f495de03e9d", "status": "passed", "time": {"start": 1755710415835, "stop": 1755710436723, "duration": 20888}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "125ccc5d6c2cd29e3eff4f495de03e9d"}, {"name": "test_a_photo_of_a_transparent_glass_cup", "children": [{"name": "测试A photo of a transparent glass cup ", "uid": "4772631ef1362a3", "parentUid": "b32afb34a72d8a3b432ccecaeb467c06", "status": "passed", "time": {"start": 1755710451241, "stop": 1755710471748, "duration": 20507}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b32afb34a72d8a3b432ccecaeb467c06"}, {"name": "test_a_sports_car_is_parked_on_the_street_side", "children": [{"name": "测试A sports car is parked on the street side", "uid": "6f89a476c469d526", "parentUid": "e8e5fdd20e1395cd53e5a7bc120140da", "status": "passed", "time": {"start": 1755710486077, "stop": 1755710506595, "duration": 20518}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e8e5fdd20e1395cd53e5a7bc120140da"}, {"name": "test_call_mom", "children": [{"name": "测试call mom", "uid": "5e14fb7d745f15a4", "parentUid": "69781a4c71a996e5064410023e23e4af", "status": "failed", "time": {"start": 1755710521217, "stop": 1755710551323, "duration": 30106}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "69781a4c71a996e5064410023e23e4af"}, {"name": "test_call_number_by_whatsapp", "children": [{"name": "测试call number by whatsapp能正常执行", "uid": "a33f0f1e44b65ad8", "parentUid": "6e03192eee0fc1e49b67b96aab8f4f9d", "status": "failed", "time": {"start": 1755710565547, "stop": 1755710594408, "duration": 28861}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e03192eee0fc1e49b67b96aab8f4f9d"}, {"name": "test_can_u_check_the_notebook", "children": [{"name": "测试can u check the notebook", "uid": "8e790f7f798cb38b", "parentUid": "411f9a4aa5205c0bfd9405f0b10d5637", "status": "passed", "time": {"start": 1755710609027, "stop": 1755710636987, "duration": 27960}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "411f9a4aa5205c0bfd9405f0b10d5637"}, {"name": "test_change_female_tone_name_voice", "children": [{"name": "测试change (female/tone name) voice能正常执行", "uid": "922bf33b821ac18f", "parentUid": "e304012317c1b3a1f774488d1d79d607", "status": "passed", "time": {"start": 1755710651429, "stop": 1755710672906, "duration": 21477}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e304012317c1b3a1f774488d1d79d607"}, {"name": "test_change_man_voice", "children": [{"name": "测试change man voice能正常执行", "uid": "783d1b35174d91e4", "parentUid": "9a2cbc470fe28892afde5493b609c218", "status": "passed", "time": {"start": 1755710686900, "stop": 1755710707420, "duration": 20520}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9a2cbc470fe28892afde5493b609c218"}, {"name": "test_change_your_voice", "children": [{"name": "测试change your voice能正常执行", "uid": "5156b101e511d7c4", "parentUid": "0aec3e6cbfb649afcf2d7319501ab2f5", "status": "passed", "time": {"start": 1755710721885, "stop": 1755710742518, "duration": 20633}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0aec3e6cbfb649afcf2d7319501ab2f5"}, {"name": "test_check_battery_information", "children": [{"name": "测试check battery information返回正确的不支持响应", "uid": "86e30c2e6222b38", "parentUid": "8757fd926e61f6ebb245d3d3d474c927", "status": "passed", "time": {"start": 1755710757053, "stop": 1755710777963, "duration": 20910}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8757fd926e61f6ebb245d3d3d474c927"}, {"name": "test_check_contact", "children": [{"name": "测试check contact能正常执行", "uid": "1338a64919f814e3", "parentUid": "a40100045be74641430d892fba85ca45", "status": "passed", "time": {"start": 1755710792191, "stop": 1755710821179, "duration": 28988}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a40100045be74641430d892fba85ca45"}, {"name": "test_check_contacts", "children": [{"name": "测试check contacts能正常执行", "uid": "5f8ee9e3f5d79dcb", "parentUid": "2d2788a266628ad79e779a5222860ff2", "status": "passed", "time": {"start": 1755710835886, "stop": 1755710864916, "duration": 29030}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2d2788a266628ad79e779a5222860ff2"}, {"name": "test_check_mobile_data_balance_of_sim", "children": [{"name": "测试check mobile data balance of sim2返回正确的不支持响应", "uid": "949ca3c8b711de72", "parentUid": "837efc09915a750bd95f96f1811eb69d", "status": "failed", "time": {"start": 1755710879509, "stop": 1755710900214, "duration": 20705}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "837efc09915a750bd95f96f1811eb69d"}, {"name": "test_check_model_information", "children": [{"name": "测试check model information返回正确的不支持响应", "uid": "327dcb048cc0fe4", "parentUid": "78831bedd8240ff30a47ed4c298163e6", "status": "passed", "time": {"start": 1755710915104, "stop": 1755710935537, "duration": 20433}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "78831bedd8240ff30a47ed4c298163e6"}, {"name": "test_check_my_balance_of_sim", "children": [{"name": "测试check my balance of sim1返回正确的不支持响应", "uid": "ff1097fbee878942", "parentUid": "bddf49f414a6afcdd8f3aa88c2990b73", "status": "passed", "time": {"start": 1755710950108, "stop": 1755710970835, "duration": 20727}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bddf49f414a6afcdd8f3aa88c2990b73"}, {"name": "test_check_my_to_do_list", "children": [{"name": "测试check my to-do list能正常执行", "uid": "2cadbc215e77f7e9", "parentUid": "941e24236863d467719d0e685e22e88f", "status": "passed", "time": {"start": 1755710985078, "stop": 1755711006198, "duration": 21120}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "941e24236863d467719d0e685e22e88f"}, {"name": "test_check_ram_information", "children": [{"name": "测试check ram information", "uid": "34e141b2776ec6e3", "parentUid": "80af6b2bcd88de0caeb66147d7b56775", "status": "passed", "time": {"start": 1755711020853, "stop": 1755711041522, "duration": 20669}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "80af6b2bcd88de0caeb66147d7b56775"}, {"name": "test_check_rear_camera_information", "children": [{"name": "测试check rear camera information能正常执行", "uid": "c4331d4423266ee9", "parentUid": "0f68e4cddcec3d7808a4b93d7289e24d", "status": "passed", "time": {"start": 1755711056075, "stop": 1755711076900, "duration": 20825}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0f68e4cddcec3d7808a4b93d7289e24d"}, {"name": "test_check_system_update", "children": [{"name": "测试check system update", "uid": "a155cce1034230a3", "parentUid": "0275cb25e1cc42b06e4cc93e842b4b50", "status": "passed", "time": {"start": 1755711091508, "stop": 1755711112221, "duration": 20713}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0275cb25e1cc42b06e4cc93e842b4b50"}, {"name": "test_close_equilibrium_mode", "children": [{"name": "测试close equilibrium mode返回正确的不支持响应", "uid": "f76e37bafb506cca", "parentUid": "2a21ec0aa1162e7205ed4479032eec28", "status": "passed", "time": {"start": 1755711126465, "stop": 1755711147400, "duration": 20935}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2a21ec0aa1162e7205ed4479032eec28"}, {"name": "test_close_performance_mode", "children": [{"name": "测试close performance mode返回正确的不支持响应", "uid": "66f53df8d11303cb", "parentUid": "95753a1df5aed1e2d1f53aaac547d09e", "status": "passed", "time": {"start": 1755711161634, "stop": 1755711181723, "duration": 20089}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "95753a1df5aed1e2d1f53aaac547d09e"}, {"name": "test_close_power_saving_mode", "children": [{"name": "测试close power saving mode返回正确的不支持响应", "uid": "9b44bb9381e76543", "parentUid": "cc7047e4d72a0d0f3090e6371b0aadaf", "status": "passed", "time": {"start": 1755711196322, "stop": 1755711217227, "duration": 20905}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cc7047e4d72a0d0f3090e6371b0aadaf"}, {"name": "test_design_a_high_end_jewelry_ring_themed_around_flamingo_elements_for_e_commerce_illustrations", "children": [{"name": "测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations", "uid": "d8c8fcb4df01608b", "parentUid": "f3c878dcefcd898c51bcf86ea1fe78fa", "status": "failed", "time": {"start": 1755711231762, "stop": 1755711255108, "duration": 23346}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f3c878dcefcd898c51bcf86ea1fe78fa"}, {"name": "test_dial_the_number_on_the_screen", "children": [{"name": "测试Dial the number on the screen", "uid": "f88d0ff2665d71d8", "parentUid": "9e3a850134620a6df84f6f9e714c5644", "status": "passed", "time": {"start": 1755711269779, "stop": 1755711290342, "duration": 20563}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e3a850134620a6df84f6f9e714c5644"}, {"name": "test_disable_accelerate_dialogue", "children": [{"name": "测试disable accelerate dialogue返回正确的不支持响应", "uid": "63fa6ccec7fad5eb", "parentUid": "eb416fd31892d7985df8f4a00fa61281", "status": "passed", "time": {"start": 1755711304552, "stop": 1755711324987, "duration": 20435}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eb416fd31892d7985df8f4a00fa61281"}, {"name": "test_disable_all_ai_magic_box_features", "children": [{"name": "测试disable all ai magic box features返回正确的不支持响应", "uid": "e814f84e67086f9f", "parentUid": "ae74f6051eb946be44a497a5345a6122", "status": "passed", "time": {"start": 1755711339752, "stop": 1755711360642, "duration": 20890}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ae74f6051eb946be44a497a5345a6122"}, {"name": "test_disable_auto_pickup", "children": [{"name": "测试disable auto pickup返回正确的不支持响应", "uid": "754158780744d744", "parentUid": "12db62453bb023c9292784bafa6f3501", "status": "passed", "time": {"start": 1755711375384, "stop": 1755711395648, "duration": 20264}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12db62453bb023c9292784bafa6f3501"}, {"name": "test_disable_brightness_locking", "children": [{"name": "测试disable brightness locking返回正确的不支持响应", "uid": "d6a1fd0c70f47e0f", "parentUid": "3dcacd9d4311d09538f3bfd9137d3407", "status": "passed", "time": {"start": 1755711410121, "stop": 1755711430728, "duration": 20607}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3dcacd9d4311d09538f3bfd9137d3407"}, {"name": "test_disable_call_rejection", "children": [{"name": "测试disable call rejection返回正确的不支持响应", "uid": "17d2a0a003d6d160", "parentUid": "2df8406295adf2f3ca8780a6fd181bd8", "status": "passed", "time": {"start": 1755711445164, "stop": 1755711475678, "duration": 30514}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2df8406295adf2f3ca8780a6fd181bd8"}, {"name": "test_disable_hide_notifications", "children": [{"name": "测试disable hide notifications返回正确的不支持响应", "uid": "657ed2213e67f17f", "parentUid": "4f012dc2e8bb211fd5ead2f1f23cd42d", "status": "passed", "time": {"start": 1755711490529, "stop": 1755711511192, "duration": 20663}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4f012dc2e8bb211fd5ead2f1f23cd42d"}, {"name": "test_disable_magic_voice_changer", "children": [{"name": "测试disable magic voice changer返回正确的不支持响应", "uid": "69e17ba55154faec", "parentUid": "a6c9b5ce2532611959a8b1cf0b7abdf0", "status": "passed", "time": {"start": 1755711525591, "stop": 1755711546116, "duration": 20525}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a6c9b5ce2532611959a8b1cf0b7abdf0"}, {"name": "test_disable_network_enhancement", "children": [{"name": "测试disable network enhancement返回正确的不支持响应", "uid": "7ebf02b247aaeca5", "parentUid": "4c36c5378c3644d7cdbf63c08440a304", "status": "passed", "time": {"start": 1755711560532, "stop": 1755711581398, "duration": 20866}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4c36c5378c3644d7cdbf63c08440a304"}, {"name": "test_disable_running_lock", "children": [{"name": "测试disable running lock返回正确的不支持响应", "uid": "b7ad49db78ad9e00", "parentUid": "bdd8ac18c65aee858c82eb5de9f7b502", "status": "passed", "time": {"start": 1755711596134, "stop": 1755711618061, "duration": 21927}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bdd8ac18c65aee858c82eb5de9f7b502"}, {"name": "test_disable_touch_optimization", "children": [{"name": "测试disable touch optimization返回正确的不支持响应", "uid": "b94f6474b6238d1a", "parentUid": "7777d1894ae8ebdcc0703d8d7a1d888d", "status": "passed", "time": {"start": 1755711632893, "stop": 1755711653385, "duration": 20492}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7777d1894ae8ebdcc0703d8d7a1d888d"}, {"name": "test_disable_unfreeze", "children": [{"name": "测试disable unfreeze返回正确的不支持响应", "uid": "960cdd0515597ad8", "parentUid": "f4f38e300a9eadad2128e98bc9820a33", "status": "passed", "time": {"start": 1755711667964, "stop": 1755711688997, "duration": 21033}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f4f38e300a9eadad2128e98bc9820a33"}, {"name": "test_disable_zonetouch_master", "children": [{"name": "测试disable zonetouch master返回正确的不支持响应", "uid": "cd18059c198a0709", "parentUid": "b2b3f74c91ec8af12a7b70a1858cb09f", "status": "passed", "time": {"start": 1755711703536, "stop": 1755711723927, "duration": 20391}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b2b3f74c91ec8af12a7b70a1858cb09f"}, {"name": "test_download_in_play_store", "children": [{"name": "测试download in play store", "uid": "e5df7cd0fdfccd2c", "parentUid": "ab04619dbfb0da204448c4c9be7b6a4f", "status": "passed", "time": {"start": 1755711738428, "stop": 1755711764360, "duration": 25932}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ab04619dbfb0da204448c4c9be7b6a4f"}, {"name": "test_download_in_playstore", "children": [{"name": "测试download in playstore", "uid": "b50886ca1fa2d560", "parentUid": "3b042038f79bb86f8776a7ddaad8e1fb", "status": "passed", "time": {"start": 1755711779136, "stop": 1755711805444, "duration": 26308}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3b042038f79bb86f8776a7ddaad8e1fb"}, {"name": "test_download_whatsapp", "children": [{"name": "测试download whatsapp能正常执行", "uid": "4d31fe080feb4515", "parentUid": "1f0c2d58931bbc9fa753a998f57e6ee2", "status": "passed", "time": {"start": 1755711820163, "stop": 1755711845606, "duration": 25443}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1f0c2d58931bbc9fa753a998f57e6ee2"}, {"name": "test_driving_mode", "children": [{"name": "测试driving mode返回正确的不支持响应", "uid": "b46b15d39e0b3b36", "parentUid": "e021bf6bc9463d8e3bd9aab9475fb30a", "status": "passed", "time": {"start": 1755711860041, "stop": 1755711880454, "duration": 20413}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e021bf6bc9463d8e3bd9aab9475fb30a"}, {"name": "test_enable_accelerate_dialogue", "children": [{"name": "测试enable accelerate dialogue返回正确的不支持响应", "uid": "ce3e4bfb4f1d431d", "parentUid": "29814f9b6b5bad3852e86d186b662dab", "status": "passed", "time": {"start": 1755711895033, "stop": 1755711916062, "duration": 21029}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "29814f9b6b5bad3852e86d186b662dab"}, {"name": "test_enable_all_ai_magic_box_features", "children": [{"name": "测试enable all ai magic box features返回正确的不支持响应", "uid": "49c7a831e04edfb5", "parentUid": "9b7a1bd9dfc15c1c490b49730b3b06c6", "status": "passed", "time": {"start": 1755711930713, "stop": 1755711951175, "duration": 20462}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9b7a1bd9dfc15c1c490b49730b3b06c6"}, {"name": "test_enable_auto_pickup", "children": [{"name": "测试enable auto pickup返回正确的不支持响应", "uid": "795650584e329424", "parentUid": "34950724c1fa063bc98eef873eb35771", "status": "passed", "time": {"start": 1755711965465, "stop": 1755711985632, "duration": 20167}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "34950724c1fa063bc98eef873eb35771"}, {"name": "test_enable_brightness_locking", "children": [{"name": "测试enable brightness locking返回正确的不支持响应", "uid": "dc9110b4876a727d", "parentUid": "fb01cd9d24d4da8385ec6db44b9bf596", "status": "passed", "time": {"start": 1755712000187, "stop": 1755712021067, "duration": 20880}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb01cd9d24d4da8385ec6db44b9bf596"}, {"name": "test_enable_call_on_hold", "children": [{"name": "测试Enable Call on Hold返回正确的不支持响应", "uid": "80a57a7e444b9600", "parentUid": "743e0a22021355320010c1959207f737", "status": "passed", "time": {"start": 1755712036037, "stop": 1755712065049, "duration": 29012}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "743e0a22021355320010c1959207f737"}, {"name": "test_enable_call_rejection", "children": [{"name": "测试Enable Call Rejection返回正确的不支持响应", "uid": "35dde70f35bbfe9b", "parentUid": "a7a8abf2aae8cc5f45b540e4f777f037", "status": "passed", "time": {"start": 1755712079475, "stop": 1755712107770, "duration": 28295}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a7a8abf2aae8cc5f45b540e4f777f037"}, {"name": "test_enable_network_enhancement", "children": [{"name": "测试Enable Network Enhancement返回正确的不支持响应", "uid": "e08a284bddf2d94", "parentUid": "ecc995aaaa028fb08ab7ae1ab0e7875d", "status": "passed", "time": {"start": 1755712122091, "stop": 1755712143242, "duration": 21151}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ecc995aaaa028fb08ab7ae1ab0e7875d"}, {"name": "test_enable_running_lock", "children": [{"name": "测试enable running lock返回正确的不支持响应", "uid": "6ac4bcf2b2320edd", "parentUid": "d576b618126fc7256f34985c398c1d3b", "status": "passed", "time": {"start": 1755712157747, "stop": 1755712180117, "duration": 22370}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d576b618126fc7256f34985c398c1d3b"}, {"name": "test_enable_touch_optimization", "children": [{"name": "测试enable touch optimization返回正确的不支持响应", "uid": "42fda6eca650eb29", "parentUid": "8480a7d7a44bc6838b96d68a4c18aa05", "status": "passed", "time": {"start": 1755712194449, "stop": 1755712214581, "duration": 20132}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8480a7d7a44bc6838b96d68a4c18aa05"}, {"name": "test_enable_unfreeze", "children": [{"name": "测试enable unfreeze返回正确的不支持响应", "uid": "fa0470f75fef5cb4", "parentUid": "66a93da14003d58b4dfb0006f98aa4c5", "status": "passed", "time": {"start": 1755712229032, "stop": 1755712249507, "duration": 20475}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "66a93da14003d58b4dfb0006f98aa4c5"}, {"name": "test_enable_zonetouch_master", "children": [{"name": "测试enable zonetouch master返回正确的不支持响应", "uid": "7899813e415fcd74", "parentUid": "8c6899d4ddce4d928d89f60cb07aeea9", "status": "passed", "time": {"start": 1755712263867, "stop": 1755712285139, "duration": 21272}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c6899d4ddce4d928d89f60cb07aeea9"}, {"name": "test_end_exercising", "children": [{"name": "测试end exercising能正常执行", "uid": "54fb5a2d17f75684", "parentUid": "a21fb30a814209d2df9c14775968c587", "status": "passed", "time": {"start": 1755712299813, "stop": 1755712321431, "duration": 21618}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a21fb30a814209d2df9c14775968c587"}, {"name": "test_extend_the_image", "children": [{"name": "测试extend the image能正常执行", "uid": "42e22ad7e13fa77", "parentUid": "656e7206a16e23ae119789777b609183", "status": "failed", "time": {"start": 1755712336003, "stop": 1755712356170, "duration": 20167}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "656e7206a16e23ae119789777b609183"}, {"name": "test_flat_illustration_of_a_girl_background_in_avocado_green_minimalist_art_white_dress_red_lipstick_alluring_gaze_green_vintage_earrings_profile_view_soft_lighting_muted_tones_serene_ambiance", "children": [{"name": "测试flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.", "uid": "427c0e6c562a36a3", "parentUid": "b958aba74a145b539d7044e12999e87d", "status": "passed", "time": {"start": 1755712370859, "stop": 1755712392060, "duration": 21201}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b958aba74a145b539d7044e12999e87d"}, {"name": "test_generate_a_cartoon_style_puppy_image_for_me_with_a_4_3_aspect_ratio", "children": [{"name": "测试Generate a cartoon-style puppy image for me with a 4:3 aspect ratio", "uid": "d683cbee37868329", "parentUid": "158f9e5ecd83a63dfb841fa3bf07b617", "status": "passed", "time": {"start": 1755712406479, "stop": 1755712426861, "duration": 20382}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "158f9e5ecd83a63dfb841fa3bf07b617"}, {"name": "test_generate_a_circular_car_logo_image_with_a_three_pointed_star_inside_the_logo", "children": [{"name": "测试Generate a circular car logo image with a three-pointed star inside the logo", "uid": "6f18513442b4a26d", "parentUid": "10aa45bdca32abb792f72299d7d1602a", "status": "passed", "time": {"start": 1755712441418, "stop": 1755712461689, "duration": 20271}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "10aa45bdca32abb792f72299d7d1602a"}, {"name": "test_generate_a_landscape_painting_image_for_me", "children": [{"name": "测试Generate a landscape painting image for me", "uid": "cc7da05a519e4d8f", "parentUid": "cd614964dd5077ebe6826f8a658328d5", "status": "failed", "time": {"start": 1755712476327, "stop": 1755712497053, "duration": 20726}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cd614964dd5077ebe6826f8a658328d5"}, {"name": "test_generate_a_picture_in_the_night_forest_for_me", "children": [{"name": "测试Generate a picture in the night forest for me", "uid": "f4ec35d405befac3", "parentUid": "318e0edf3649f85c5115c2f373b5c98e", "status": "passed", "time": {"start": 1755712512018, "stop": 1755712532438, "duration": 20420}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "318e0edf3649f85c5115c2f373b5c98e"}, {"name": "test_generate_a_picture_of_a_jungle_stream_for_me", "children": [{"name": "测试Generate a picture of a jungle stream for me", "uid": "17d74d81d04a6a9d", "parentUid": "c5a9ca5935e3a68d59f85b9ff612c907", "status": "passed", "time": {"start": 1755712546685, "stop": 1755712567065, "duration": 20380}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c5a9ca5935e3a68d59f85b9ff612c907"}, {"name": "test_generate_an_image_of_a_chubby_orange_cat_chef", "children": [{"name": "测试Generate an image of a chubby orange cat chef with a round body and an endearing appearance", "uid": "def9a07194c6a68d", "parentUid": "968ed53b3fe60b381f5f61bec75c01db", "status": "passed", "time": {"start": 1755712581668, "stop": 1755712602289, "duration": 20621}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "968ed53b3fe60b381f5f61bec75c01db"}, {"name": "test_go_home", "children": [{"name": "测试go home能正常执行", "uid": "4d6edbca908730fe", "parentUid": "4a5220f470f11d89726e23ef156eb88b", "status": "passed", "time": {"start": 1755712617136, "stop": 1755712637767, "duration": 20631}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a5220f470f11d89726e23ef156eb88b"}, {"name": "test_go_to_office", "children": [{"name": "测试go to office", "uid": "c4e2031bf63fbd80", "parentUid": "17ff186ceded764016c0155f25eb3375", "status": "passed", "time": {"start": 1755712652462, "stop": 1755712673122, "duration": 20660}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "17ff186ceded764016c0155f25eb3375"}, {"name": "test_gold_coin_rain", "children": [{"name": "测试gold coin rain能正常执行", "uid": "d216d263e31042c2", "parentUid": "4ff40e880339f30d1929194918d94abe", "status": "passed", "time": {"start": 1755712687722, "stop": 1755712712211, "duration": 24489}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4ff40e880339f30d1929194918d94abe"}, {"name": "test_hamster_mascot", "children": [{"name": "测试hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.", "uid": "8c91527bbabd6716", "parentUid": "ac616b983471f9d0a7ed28334712daea", "status": "failed", "time": {"start": 1755712726345, "stop": 1755712747925, "duration": 21580}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ac616b983471f9d0a7ed28334712daea"}, {"name": "test_happy_new_year", "children": [{"name": "测试happy new year能正常执行", "uid": "959838ca0b62c989", "parentUid": "a0b2ddf900ecce63d74a6e5a2048669c", "status": "passed", "time": {"start": 1755712762458, "stop": 1755712786139, "duration": 23681}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a0b2ddf900ecce63d74a6e5a2048669c"}, {"name": "test_hello_hello", "children": [{"name": "测试hello hello能正常执行", "uid": "df6d3dd2ff5385ab", "parentUid": "79da41b110afe1954f2965621e69bebc", "status": "passed", "time": {"start": 1755712800643, "stop": 1755712824978, "duration": 24335}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "79da41b110afe1954f2965621e69bebc"}, {"name": "test_help_generate_a_picture_of_ancient_city", "children": [{"name": "测试A furry little monkey", "uid": "535c8ebe7b4c0300", "parentUid": "5d23429c1981e990d47168a7d4242b52", "status": "passed", "time": {"start": 1755712839550, "stop": 1755712859910, "duration": 20360}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5d23429c1981e990d47168a7d4242b52"}, {"name": "test_help_me_generate_a_3D_rendered_picture_of_a_Song_style_palace_surrounded_by_auspicious_clouds_and_with_delicate_colors", "children": [{"name": "测试Help me generate a 3D rendered picture of a Song-style palace surrounded by auspicious clouds and with delicate colors", "uid": "63e40151836706cb", "parentUid": "1c7cbdeca2def7d1f1eda877c8cb7276", "status": "passed", "time": {"start": 1755712874437, "stop": 1755712894647, "duration": 20210}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1c7cbdeca2def7d1f1eda877c8cb7276"}, {"name": "test_help_me_generate_a_photo_of_a_fully_nude_boy", "children": [{"name": "测试Help me generate a photo of a fully nude boy", "uid": "ef8f26c2041d11be", "parentUid": "4a7c46ca503f2c509fdb86d6ce5f77a8", "status": "passed", "time": {"start": 1755712909088, "stop": 1755712930580, "duration": 21492}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a7c46ca503f2c509fdb86d6ce5f77a8"}, {"name": "test_help_me_generate_a_picture_of_a_bamboo_forest_stream", "children": [{"name": "测试help me generate a picture of a bamboo forest stream", "uid": "3b0f87ed2ba47792", "parentUid": "59c1a46c44f4d208182b82cd7fd56466", "status": "passed", "time": {"start": 1755712944825, "stop": 1755712965147, "duration": 20322}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "59c1a46c44f4d208182b82cd7fd56466"}, {"name": "test_help_me_generate_a_picture_of_a_puppy", "children": [{"name": "测试help me generate a picture of a puppy", "uid": "3d2e13bc086ec514", "parentUid": "a771820a4e3abaa61c6636c3889b02b9", "status": "passed", "time": {"start": 1755712979640, "stop": 1755712999712, "duration": 20072}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a771820a4e3abaa61c6636c3889b02b9"}, {"name": "test_help_me_generate_a_picture_of_a_white_facial_cleanser", "children": [{"name": "测试help me generate a picture of a white facial cleanser product advertisement", "uid": "d407cfa53b8805dd", "parentUid": "c8f1941d2f772ac665fc3e12896b1a98", "status": "passed", "time": {"start": 1755713014263, "stop": 1755713035343, "duration": 21080}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c8f1941d2f772ac665fc3e12896b1a98"}, {"name": "test_help_me_generate_a_picture_of_an_airplane", "children": [{"name": "测试help me generate a picture of an airplane", "uid": "a415f76a255b6a92", "parentUid": "ca1448a1bc27323ba1cb66335d0ac56a", "status": "passed", "time": {"start": 1755713050221, "stop": 1755713070693, "duration": 20472}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ca1448a1bc27323ba1cb66335d0ac56a"}, {"name": "test_help_me_generate_a_picture_of_an_elegant_girl", "children": [{"name": "测试help me generate a picture of an elegant girl", "uid": "c8e8c9cb0c056565", "parentUid": "69b56d7b8b05dbd51933cf6938544bdf", "status": "passed", "time": {"start": 1755713085071, "stop": 1755713105407, "duration": 20336}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "69b56d7b8b05dbd51933cf6938544bdf"}, {"name": "test_help_me_generate_a_picture_of_blue_and_gold_landscape", "children": [{"name": "测试help me generate a picture of blue and gold landscape", "uid": "9b688eef9a94b838", "parentUid": "ee4e872810d71bc5dda09924d1a91c4e", "status": "passed", "time": {"start": 1755713120019, "stop": 1755713141026, "duration": 21007}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ee4e872810d71bc5dda09924d1a91c4e"}, {"name": "test_help_me_generate_a_picture_of_green_trees_in_shade_and_distant_mountains_in_a_hazy_state", "children": [{"name": "测试help me generate a picture of green trees in shade and distant mountains in a hazy state", "uid": "4f7337a8b7498257", "parentUid": "8c43634d02c4b93421e468daca65f6f3", "status": "passed", "time": {"start": 1755713155584, "stop": 1755713175967, "duration": 20383}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c43634d02c4b93421e468daca65f6f3"}, {"name": "test_help_me_generate_an_image_of_the_shanghai_oriental_pearl_tower", "children": [{"name": "测试Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x'", "uid": "16d9c17ea798fd08", "parentUid": "bd550d62fd6ab24a654023a07141bc5b", "status": "passed", "time": {"start": 1755713190510, "stop": 1755713210819, "duration": 20309}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bd550d62fd6ab24a654023a07141bc5b"}, {"name": "test_help_me_write_an_email", "children": [{"name": "测试help me write an email能正常执行", "uid": "33c23c78138d1c18", "parentUid": "ef47f41b350d73a6140e48381c877229", "status": "passed", "time": {"start": 1755713225256, "stop": 1755713248389, "duration": 23133}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ef47f41b350d73a6140e48381c877229"}, {"name": "test_help_me_write_an_thanks_email", "children": [{"name": "测试help me write an thanks email能正常执行", "uid": "29d552d1101b873b", "parentUid": "b693f3729a9fcd922c000405462cc41b", "status": "failed", "time": {"start": 1755713262846, "stop": 1755713285052, "duration": 22206}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b693f3729a9fcd922c000405462cc41b"}, {"name": "test_help_me_write_an_thanks_letter", "children": [{"name": "测试help me write an thanks letter能正常执行", "uid": "1a010c5ee19b6419", "parentUid": "bc1e1c7d2a07be4f8bd829a14683b10e", "status": "passed", "time": {"start": 1755713299846, "stop": 1755713321399, "duration": 21553}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bc1e1c7d2a07be4f8bd829a14683b10e"}, {"name": "test_how_to_set_screenshots", "children": [{"name": "测试how to set screenshots返回正确的不支持响应", "uid": "cb9480142c2cdbd4", "parentUid": "98125eb3c0eb347bf93f6ebbb14e8847", "status": "passed", "time": {"start": 1755713336030, "stop": 1755713356947, "duration": 20917}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "98125eb3c0eb347bf93f6ebbb14e8847"}, {"name": "test_i_am_your_voice_assistant", "children": [{"name": "测试i am your voice assistant", "uid": "878d84142f96b7be", "parentUid": "9436cb3786c5c99cd02eae55e104db19", "status": "failed", "time": {"start": 1755713371662, "stop": 1755713392497, "duration": 20835}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9436cb3786c5c99cd02eae55e104db19"}, {"name": "test_i_think_the_screen_is_a_bit_dark_now_could_you_please_help_me_brighten_it_up", "children": [{"name": "测试I think the screen is a bit dark now. Could you please help me brighten it up?能正常执行", "uid": "377e209c6953434d", "parentUid": "b65ff6714d0bd5e3a66cd4a7fc84cc57", "status": "passed", "time": {"start": 1755713407064, "stop": 1755713427864, "duration": 20800}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b65ff6714d0bd5e3a66cd4a7fc84cc57"}, {"name": "test_i_wanna_use_sim", "children": [{"name": "测试open settings", "uid": "2c1bb67e3f12871c", "parentUid": "7b8e3220ca81e685a8104d748e3eb4e8", "status": "passed", "time": {"start": 1755713442091, "stop": 1755713478381, "duration": 36290}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b8e3220ca81e685a8104d748e3eb4e8"}, {"name": "test_i_want_make_a_video_call_to", "children": [{"name": "测试i want make a video call to能正常执行", "uid": "b09602dc5cc09fc5", "parentUid": "196f42775838d3c3c33e90a1d31b3ac3", "status": "failed", "time": {"start": 1755713492907, "stop": 1755713521466, "duration": 28559}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "196f42775838d3c3c33e90a1d31b3ac3"}, {"name": "test_i_want_to_hear_a_joke", "children": [{"name": "测试i want to hear a joke能正常执行", "uid": "751695afb4b430bd", "parentUid": "c70a6aa867b7400f339364fb25312211", "status": "passed", "time": {"start": 1755713536544, "stop": 1755713557931, "duration": 21387}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c70a6aa867b7400f339364fb25312211"}, {"name": "test_increase_settings_for_special_functions", "children": [{"name": "测试increase settings for special functions返回正确的不支持响应", "uid": "554df24716ad09c5", "parentUid": "849c4d59d6ccd19947474d581af3ccb2", "status": "passed", "time": {"start": 1755713572625, "stop": 1755713593792, "duration": 21167}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "849c4d59d6ccd19947474d581af3ccb2"}, {"name": "test_install_whatsapp", "children": [{"name": "测试install whatsapp", "uid": "40f21bb9bcc58571", "parentUid": "b0eca47fb2f8f32ac7a1ae66ba395e2e", "status": "passed", "time": {"start": 1755713608235, "stop": 1755713634587, "duration": 26352}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b0eca47fb2f8f32ac7a1ae66ba395e2e"}, {"name": "test_it_wears_a_red_leather_collar", "children": [{"name": "测试it wears a red leather collar", "uid": "b1ec069839ac462f", "parentUid": "8882bfaab2f11ddb12771524426ec7d8", "status": "passed", "time": {"start": 1755713649156, "stop": 1755713670025, "duration": 20869}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8882bfaab2f11ddb12771524426ec7d8"}, {"name": "test_it_wears_a_yellow_leather_collar", "children": [{"name": "测试it wears a yellow leather collar", "uid": "886ff3ab35ea4e2d", "parentUid": "9e3b03020c4faae9aed71f7803e4f1df", "status": "passed", "time": {"start": 1755713684217, "stop": 1755713705584, "duration": 21367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e3b03020c4faae9aed71f7803e4f1df"}, {"name": "test_jump_to_adaptive_brightness_settings", "children": [{"name": "测试jump to adaptive brightness settings返回正确的不支持响应", "uid": "54ac4d0e60138b59", "parentUid": "0355ddc9170b8d072304068f8868f806", "status": "passed", "time": {"start": 1755713720283, "stop": 1755713741306, "duration": 21023}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0355ddc9170b8d072304068f8868f806"}, {"name": "test_jump_to_ai_wallpaper_generator_settings", "children": [{"name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "uid": "2c089ad2a03c802d", "parentUid": "fb66326ff650d5ed24178fe23a3ec6c7", "status": "passed", "time": {"start": 1755713755631, "stop": 1755713776536, "duration": 20905}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb66326ff650d5ed24178fe23a3ec6c7"}, {"name": "test_jump_to_auto_rotate_screen_settings", "children": [{"name": "测试jump to auto rotate screen settings返回正确的不支持响应", "uid": "89ffd0d94da459ee", "parentUid": "4868992c324cca3aa80f892f5012edcc", "status": "passed", "time": {"start": 1755713791059, "stop": 1755713811626, "duration": 20567}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4868992c324cca3aa80f892f5012edcc"}, {"name": "test_jump_to_battery_and_power_saving", "children": [{"name": "测试jump to battery and power saving返回正确的不支持响应", "uid": "2e2cd6de3dca64ae", "parentUid": "c39df595d254493d9e1cf180846ee18a", "status": "passed", "time": {"start": 1755713825711, "stop": 1755713847128, "duration": 21417}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c39df595d254493d9e1cf180846ee18a"}, {"name": "test_jump_to_battery_usage", "children": [{"name": "测试jump to battery usage返回正确的不支持响应", "uid": "b51ab84faafc6791", "parentUid": "0bd7fb8ef7e8ca2e3a1a1699257e50f9", "status": "passed", "time": {"start": 1755713861870, "stop": 1755713882539, "duration": 20669}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0bd7fb8ef7e8ca2e3a1a1699257e50f9"}, {"name": "test_jump_to_call_notifications", "children": [{"name": "测试jump to call notifications返回正确的不支持响应", "uid": "f7750a7f3a5ebc38", "parentUid": "5a05d3bb6cee7f72819e49d4f6d5ee1f", "status": "passed", "time": {"start": 1755713897398, "stop": 1755713926872, "duration": 29474}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5a05d3bb6cee7f72819e49d4f6d5ee1f"}, {"name": "test_jump_to_high_brightness_mode_settings", "children": [{"name": "测试jump to high brightness mode settings返回正确的不支持响应", "uid": "9e5da72063ffe331", "parentUid": "4a8b87bfde2b2e8ce7967634be5288ab", "status": "passed", "time": {"start": 1755713941081, "stop": 1755713962176, "duration": 21095}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a8b87bfde2b2e8ce7967634be5288ab"}, {"name": "test_jump_to_lock_screen_notification_and_display_settings", "children": [{"name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "uid": "ad8b964c2c7277f2", "parentUid": "cfbc1116cbdd7d5906a09ffc432b57db", "status": "passed", "time": {"start": 1755713976957, "stop": 1755713997713, "duration": 20756}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cfbc1116cbdd7d5906a09ffc432b57db"}, {"name": "test_jump_to_nfc_settings", "children": [{"name": "测试jump to nfc settings", "uid": "6a9dd58982559231", "parentUid": "0285fb88f545482d120dd7eeddef468a", "status": "passed", "time": {"start": 1755714012004, "stop": 1755714040036, "duration": 28032}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0285fb88f545482d120dd7eeddef468a"}, {"name": "test_jump_to_notifications_and_status_bar_settings", "children": [{"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "uid": "e74646cd5997be55", "parentUid": "f6098b8dc4a6271dec4f5e5f57bd4752", "status": "passed", "time": {"start": 1755714054802, "stop": 1755714075379, "duration": 20577}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f6098b8dc4a6271dec4f5e5f57bd4752"}, {"name": "test_kill_whatsapp", "children": [{"name": "测试kill whatsapp能正常执行", "uid": "143173435c555345", "parentUid": "48a28da8e5e405bb518e2fc0c08602a0", "status": "passed", "time": {"start": 1755714090161, "stop": 1755714112687, "duration": 22526}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "48a28da8e5e405bb518e2fc0c08602a0"}, {"name": "test_kinkaku_ji", "children": [{"name": "测试Kinkaku-ji", "uid": "827684d8379c54c4", "parentUid": "c662375ad1f42584024973d859a56ece", "status": "passed", "time": {"start": 1755714127374, "stop": 1755714150399, "duration": 23025}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c662375ad1f42584024973d859a56ece"}, {"name": "test_make_a_call_by_whatsapp", "children": [{"name": "测试make a call by whatsapp能正常执行", "uid": "84e86790fa4b5099", "parentUid": "6f8cbc6fe3edd73cea435301bd04bc62", "status": "failed", "time": {"start": 1755714165311, "stop": 1755714194613, "duration": 29302}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6f8cbc6fe3edd73cea435301bd04bc62"}, {"name": "test_make_a_call_on_whatsapp_to_a", "children": [{"name": "测试make a call on whatsapp to a能正常执行", "uid": "6bde6f903272f86a", "parentUid": "d444662576dbd8dfa4249417991e35ca", "status": "failed", "time": {"start": 1755714209846, "stop": 1755714239752, "duration": 29906}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d444662576dbd8dfa4249417991e35ca"}, {"name": "test_make_a_phone_call_to_17621905233", "children": [{"name": "测试make a phone call to 17621905233", "uid": "92bd1b35636073c0", "parentUid": "646863d55c63f7ac85128245a6418cb8", "status": "skipped", "time": {"start": 1755714241421, "stop": 1755714241421, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='make a phone call to 17621905233命令需要先添加联系人，才能拨打电话，无法通用化')", "smoke"]}], "uid": "646863d55c63f7ac85128245a6418cb8"}, {"name": "test_merry_christmas", "children": [{"name": "测试merry christmas", "uid": "ee618ddd3097a2de", "parentUid": "640796148310e1604de99261ff5802bd", "status": "failed", "time": {"start": 1755714254873, "stop": 1755714278250, "duration": 23377}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "640796148310e1604de99261ff5802bd"}, {"name": "test_modify_grape_timbre", "children": [{"name": "测试Modify grape timbre返回正确的不支持响应", "uid": "309142b0a1a05401", "parentUid": "09910b981ee980bf81421c1efd746a52", "status": "passed", "time": {"start": 1755714293227, "stop": 1755714313661, "duration": 20434}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "09910b981ee980bf81421c1efd746a52"}, {"name": "test_more_settings", "children": [{"name": "测试more settings返回正确的不支持响应", "uid": "2e6315addcbe0091", "parentUid": "ca0b29ba7554057ab3ba8216b6b1f0ba", "status": "passed", "time": {"start": 1755714328537, "stop": 1755714351030, "duration": 22493}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ca0b29ba7554057ab3ba8216b6b1f0ba"}, {"name": "test_navigate_to_the_address_on_the_screen", "children": [{"name": "测试Navigate to the address on the screen", "uid": "3400b192c234228", "parentUid": "758224bbc0cda79cc3eb6dfa2dbb1df4", "status": "passed", "time": {"start": 1755714365612, "stop": 1755714391956, "duration": 26344}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "758224bbc0cda79cc3eb6dfa2dbb1df4"}, {"name": "test_navigation_to_the_address_in_the_image", "children": [{"name": "测试navigation to the address in thie image能正常执行", "uid": "167590b8047e5e71", "parentUid": "6e2b1fe773a9723006e50fc18b054a32", "status": "failed", "time": {"start": 1755714406421, "stop": 1755714432815, "duration": 26394}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6e2b1fe773a9723006e50fc18b054a32"}, {"name": "test_navigation_to_the_first_address_in_the_image", "children": [{"name": "测试navigation to the first address in the image能正常执行", "uid": "f0cdfab033dcf1e4", "parentUid": "930b7b34cf2d90f55ec5996ae313b76b", "status": "passed", "time": {"start": 1755714447563, "stop": 1755714473930, "duration": 26367}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "930b7b34cf2d90f55ec5996ae313b76b"}, {"name": "test_new_year_wishes", "children": [{"name": "测试new year wishes", "uid": "f1b94fecde31a774", "parentUid": "dcdb94c9beb7ae21b8fc6e0e51c601c4", "status": "passed", "time": {"start": 1755714488350, "stop": 1755714512247, "duration": 23897}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "dcdb94c9beb7ae21b8fc6e0e51c601c4"}, {"name": "test_new_year_wishs", "children": [{"name": "测试new year wishs能正常执行", "uid": "150268bbf6efdd56", "parentUid": "7e84be7c77dafee1081f62d7cfa0849c", "status": "passed", "time": {"start": 1755714526972, "stop": 1755714551414, "duration": 24442}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7e84be7c77dafee1081f62d7cfa0849c"}, {"name": "test_open_camera", "children": [{"name": "测试open camera", "uid": "3cf2f607644999ee", "parentUid": "d9ce24a54ba9cdc1f1589d44178a6275", "status": "passed", "time": {"start": 1755714565982, "stop": 1755714606300, "duration": 40318}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9ce24a54ba9cdc1f1589d44178a6275"}, {"name": "test_open_font_family_settings", "children": [{"name": "测试open font family settings返回正确的不支持响应", "uid": "b52b8c7414d02808", "parentUid": "5d1c4a01c8558acb00bb5c6e528035a3", "status": "passed", "time": {"start": 1755714621284, "stop": 1755714642428, "duration": 21144}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5d1c4a01c8558acb00bb5c6e528035a3"}, {"name": "test_open_maps", "children": [{"name": "测试open maps", "uid": "3f9822a9ba521174", "parentUid": "48a29ef2e8e06a69e79e3ce012a1ca44", "status": "passed", "time": {"start": 1755714657246, "stop": 1755714690988, "duration": 33742}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "48a29ef2e8e06a69e79e3ce012a1ca44"}, {"name": "test_open_notification_ringtone_settings", "children": [{"name": "测试open notification ringtone settings返回正确的不支持响应", "uid": "b9658b259c961db2", "parentUid": "309e531c38122a270a22145c2b2f4a8a", "status": "passed", "time": {"start": 1755714705539, "stop": 1755714726431, "duration": 20892}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "309e531c38122a270a22145c2b2f4a8a"}, {"name": "test_open_settings", "children": [{"name": "测试open settings", "uid": "2010785e0c89ed86", "parentUid": "5366ad01888b7b931dc846b46730ef41", "status": "passed", "time": {"start": 1755714741099, "stop": 1755714772241, "duration": 31142}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5366ad01888b7b931dc846b46730ef41"}, {"name": "test_open_the_settings", "children": [{"name": "测试open the settings", "uid": "e8f1a16a860d194a", "parentUid": "adb6b73b5c7aa6bc82b47a2854ec6049", "status": "passed", "time": {"start": 1755714786798, "stop": 1755714818888, "duration": 32090}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "adb6b73b5c7aa6bc82b47a2854ec6049"}, {"name": "test_open_whatsapp", "children": [{"name": "测试open whatsapp", "uid": "816c6a8b9c388749", "parentUid": "fae42569d3565f4a0509b1abe481814e", "status": "failed", "time": {"start": 1755714833356, "stop": 1755714855317, "duration": 21961}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fae42569d3565f4a0509b1abe481814e"}, {"name": "test_order_a_burger", "children": [{"name": "测试order a burger返回正确的不支持响应", "uid": "93fccfa236b74211", "parentUid": "12a853573a3ed32b45369299151ae46b", "status": "failed", "time": {"start": 1755714869937, "stop": 1755714890353, "duration": 20416}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "12a853573a3ed32b45369299151ae46b"}, {"name": "test_order_a_takeaway", "children": [{"name": "测试order a takeaway返回正确的不支持响应", "uid": "a981a970f4f7d352", "parentUid": "6116229fe40039026544aa05527a4376", "status": "failed", "time": {"start": 1755714905123, "stop": 1755714925899, "duration": 20776}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "6116229fe40039026544aa05527a4376"}, {"name": "test_parking_space", "children": [{"name": "测试parking space能正常执行", "uid": "e36f71212ac3b25e", "parentUid": "d235978673eeb20a0aa5a17d0b9af2fb", "status": "passed", "time": {"start": 1755714940752, "stop": 1755714961138, "duration": 20386}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d235978673eeb20a0aa5a17d0b9af2fb"}, {"name": "test_play_carpenters_video", "children": [{"name": "测试play carpenters'video", "uid": "ade945068fe9004d", "parentUid": "4d419e427b323d88eac6c065a5f63bf0", "status": "passed", "time": {"start": 1755714975801, "stop": 1755715002708, "duration": 26907}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4d419e427b323d88eac6c065a5f63bf0"}, {"name": "test_play_football_video_by_youtube", "children": [{"name": "测试play football video by youtube", "uid": "6a6cc1e7cd8f4a82", "parentUid": "1b38b53340d3cf404c7d3184db4b3767", "status": "passed", "time": {"start": 1755715017520, "stop": 1755715044358, "duration": 26838}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b38b53340d3cf404c7d3184db4b3767"}, {"name": "test_play_love_sotry", "children": [{"name": "测试play love sotry", "uid": "a258242e9c32e9fb", "parentUid": "3279616bb2594ec7b639e82e288080c3", "status": "passed", "time": {"start": 1755715059150, "stop": 1755715097376, "duration": 38226}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3279616bb2594ec7b639e82e288080c3"}, {"name": "test_play_music_by_Audiomack", "children": [{"name": "测试play music by Audiomack", "uid": "7a5fabed6ebd69f9", "parentUid": "f8faca32e9c6c24264e9529853e54177", "status": "failed", "time": {"start": 1755715111807, "stop": 1755715134662, "duration": 22855}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f8faca32e9c6c24264e9529853e54177"}, {"name": "test_play_taylor_swift_s_song_love_sotry", "children": [{"name": "测试play taylor swift‘s song love story", "uid": "12d1457f5f2c6ee2", "parentUid": "7c3b9cf1e80a1543e429a21b6580f11f", "status": "passed", "time": {"start": 1755715149162, "stop": 1755715181119, "duration": 31957}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c3b9cf1e80a1543e429a21b6580f11f"}, {"name": "test_play_the_album", "children": [{"name": "测试play the album", "uid": "66ef2627b330a8b5", "parentUid": "96bab08553e379e0c0029cca77667f8f", "status": "passed", "time": {"start": 1755715195542, "stop": 1755715228148, "duration": 32606}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "96bab08553e379e0c0029cca77667f8f"}, {"name": "test_play_video", "children": [{"name": "测试play video", "uid": "bbb773782dcfa0d8", "parentUid": "a38748b3b1f409e94ff601029a63f98d", "status": "passed", "time": {"start": 1755715242940, "stop": 1755715270305, "duration": 27365}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a38748b3b1f409e94ff601029a63f98d"}, {"name": "test_play_video_by_youtube", "children": [{"name": "测试play video by youtube", "uid": "9b6f40913c54816", "parentUid": "c7f371ce0f7eeb044f6c854937a7502b", "status": "passed", "time": {"start": 1755715284958, "stop": 1755715312114, "duration": 27156}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c7f371ce0f7eeb044f6c854937a7502b"}, {"name": "test_please_show_me_where_i_am", "children": [{"name": "测试please show me where i am能正常执行", "uid": "bcfaaf58d103c438", "parentUid": "a856f6c3dff3e6d986ecbf33df7fabe2", "status": "failed", "time": {"start": 1755715326769, "stop": 1755715348909, "duration": 22140}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a856f6c3dff3e6d986ecbf33df7fabe2"}, {"name": "test_pls_open_whatsapp", "children": [{"name": "测试pls open whatsapp", "uid": "70e89ac9f7ab66d7", "parentUid": "3dc012d4a4449a7636b0642c22bceb9e", "status": "failed", "time": {"start": 1755715363903, "stop": 1755715386911, "duration": 23008}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3dc012d4a4449a7636b0642c22bceb9e"}, {"name": "test_power_off_my_phone", "children": [{"name": "测试power off my phone能正常执行", "uid": "d9e70fc4fdc0e68a", "parentUid": "efc94c71c211627f11802239a7139801", "status": "skipped", "time": {"start": 1755715388628, "stop": 1755715388628, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='power off 会导致设备断开，先跳过')", "smoke"]}], "uid": "efc94c71c211627f11802239a7139801"}, {"name": "test_privacy_policy", "children": [{"name": "测试privacy policy", "uid": "cf23a689f9253a88", "parentUid": "fb018ed86c7e834a548eec9714f5e6a3", "status": "passed", "time": {"start": 1755715402341, "stop": 1755715423166, "duration": 20825}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb018ed86c7e834a548eec9714f5e6a3"}, {"name": "test_puppy", "children": [{"name": "测试puppy", "uid": "66394b6d7c195dfe", "parentUid": "44e1d788e6211ef1bf44e5b853acf76e", "status": "passed", "time": {"start": 1755715438128, "stop": 1755715459735, "duration": 21607}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "44e1d788e6211ef1bf44e5b853acf76e"}, {"name": "test_reboot_my_phone", "children": [{"name": "测试reboot my phone能正常执行", "uid": "9ca4f25a9ca3a301", "parentUid": "41272830cf23beb3b9d330466e63c528", "status": "skipped", "time": {"start": 1755715461198, "stop": 1755715461198, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='reboot 会导致设备断开，先跳过')", "smoke"]}], "uid": "41272830cf23beb3b9d330466e63c528"}, {"name": "test_redial", "children": [{"name": "测试redial", "uid": "9c279d0adf8defad", "parentUid": "494d3e1b731af3bce21d72c38e17e357", "status": "skipped", "time": {"start": 1755715461202, "stop": 1755715461202, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='redial命令需要先拨打电话，才能重拨，无法通用化')", "smoke"]}], "uid": "494d3e1b731af3bce21d72c38e17e357"}, {"name": "test_remember_the_parking_lot", "children": [{"name": "测试remember the parking lot能正常执行", "uid": "6a030dd47fe127d3", "parentUid": "57a5364756ca608e39ef1782f15a0eb6", "status": "passed", "time": {"start": 1755715474874, "stop": 1755715495589, "duration": 20715}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "57a5364756ca608e39ef1782f15a0eb6"}, {"name": "test_remember_the_parking_space", "children": [{"name": "测试remember the parking space", "uid": "621037e1a08d5de7", "parentUid": "853e3f6d6eaa9b4bad958b3da289dde7", "status": "failed", "time": {"start": 1755715510818, "stop": 1755715531762, "duration": 20944}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "853e3f6d6eaa9b4bad958b3da289dde7"}, {"name": "test_remove_the_people_from_the_image", "children": [{"name": "测试remove the people from the image", "uid": "f2c1e45e9870557e", "parentUid": "d19db6058e5f4c4097a27510ecaa9d2b", "status": "passed", "time": {"start": 1755715546687, "stop": 1755715567418, "duration": 20731}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d19db6058e5f4c4097a27510ecaa9d2b"}, {"name": "test_reset_phone", "children": [{"name": "测试reset phone返回正确的不支持响应", "uid": "68c68f0ff335b08c", "parentUid": "97792796a4c38ae78b9db124406767b2", "status": "skipped", "time": {"start": 1755715568953, "stop": 1755715568953, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke", "@pytest.mark.skip(reason='reset phone 会导致设备断开，先跳过')"]}], "uid": "97792796a4c38ae78b9db124406767b2"}, {"name": "test_restart_my_phone", "children": [{"name": "测试restart my phone能正常执行", "uid": "6463e34a97199c46", "parentUid": "c1ea683fb9d174ed7b5f8880d961bd37", "status": "skipped", "time": {"start": 1755715568959, "stop": 1755715568959, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='重启会导致设备断开，先跳过')", "smoke"]}], "uid": "c1ea683fb9d174ed7b5f8880d961bd37"}, {"name": "test_restart_the_phone", "children": [{"name": "测试restart the phone能正常执行", "uid": "41c36b3af557b8cf", "parentUid": "53adce70cf390992f02ff700aaada272", "status": "skipped", "time": {"start": 1755715568963, "stop": 1755715568963, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='重启会导致设备断开，先跳过')", "smoke"]}], "uid": "53adce70cf390992f02ff700aaada272"}, {"name": "test_running_on_the_grass", "children": [{"name": "测试running on the grass", "uid": "876cbdb653c40bdb", "parentUid": "1bc086e4c470f4bd3514200946305a81", "status": "passed", "time": {"start": 1755715582138, "stop": 1755715604713, "duration": 22575}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1bc086e4c470f4bd3514200946305a81"}, {"name": "test_search_for_addresses_on_the_screen", "children": [{"name": "测试Search for addresses on the screen能正常执行", "uid": "e3084c3fc9a93c05", "parentUid": "735baa4624c52854b8a2a73abb447f23", "status": "passed", "time": {"start": 1755715619742, "stop": 1755715640804, "duration": 21062}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "735baa4624c52854b8a2a73abb447f23"}, {"name": "test_search_the_address_in_the_image", "children": [{"name": "测试search the address in the image能正常执行", "uid": "605839e96459bcfc", "parentUid": "cba35d4f03acbacfc1c9c3b0792e089b", "status": "passed", "time": {"start": 1755715655727, "stop": 1755715676438, "duration": 20711}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cba35d4f03acbacfc1c9c3b0792e089b"}, {"name": "test_search_whatsapp_for_me", "children": [{"name": "测试search whatsapp for me能正常执行", "uid": "f002a91a86cc5d52", "parentUid": "7c7aef6b5854cef222cfb44ffc5056fe", "status": "passed", "time": {"start": 1755715691494, "stop": 1755715718074, "duration": 26580}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7c7aef6b5854cef222cfb44ffc5056fe"}, {"name": "test_searching_for_a_method_of_violent_murder", "children": [{"name": "测试searching for a method of violent murder返回正确的不支持响应", "uid": "fd391ebf1c24fdec", "parentUid": "1445db3159fb5e02b25911ddf53e9078", "status": "passed", "time": {"start": 1755715732878, "stop": 1755715753853, "duration": 20975}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1445db3159fb5e02b25911ddf53e9078"}, {"name": "test_send_my_recent_photos_to_mom_through_whatsapp", "children": [{"name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "uid": "7ecfb565ec6b27a6", "parentUid": "7904446ab7fbbc082d949ad5dd1566fc", "status": "failed", "time": {"start": 1755715768462, "stop": 1755715789733, "duration": 21271}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7904446ab7fbbc082d949ad5dd1566fc"}, {"name": "test_set_app_auto_rotate", "children": [{"name": "测试set app auto rotate返回正确的不支持响应", "uid": "54a6c07f8721dfed", "parentUid": "42a0d4edd729031d01ed4ce54f32a86f", "status": "passed", "time": {"start": 1755715804224, "stop": 1755715824936, "duration": 20712}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "42a0d4edd729031d01ed4ce54f32a86f"}, {"name": "test_set_app_notifications", "children": [{"name": "测试set app notifications返回正确的不支持响应", "uid": "85b0b9f3b49e2a52", "parentUid": "f17166693c717b681b5c4ace219a5254", "status": "passed", "time": {"start": 1755715839632, "stop": 1755715860057, "duration": 20425}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f17166693c717b681b5c4ace219a5254"}, {"name": "test_set_battery_saver_settings", "children": [{"name": "测试set battery saver settings返回正确的不支持响应", "uid": "76959a0932e6df2a", "parentUid": "90688cbd93c016ee7d407e660ac88b04", "status": "passed", "time": {"start": 1755715874734, "stop": 1755715896190, "duration": 21456}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "90688cbd93c016ee7d407e660ac88b04"}, {"name": "test_set_call_back_with_last_used_sim", "children": [{"name": "测试set call back with last used sim返回正确的不支持响应", "uid": "4de5ac35d0b605d7", "parentUid": "27c7eefc61a4f26939cc8e62add52a75", "status": "passed", "time": {"start": 1755715911170, "stop": 1755715941571, "duration": 30401}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "27c7eefc61a4f26939cc8e62add52a75"}, {"name": "test_set_color_style", "children": [{"name": "测试set color style返回正确的不支持响应", "uid": "ebb316f3e15e7982", "parentUid": "4a9eb6eed41e8d978e3f2271d25aeeb9", "status": "passed", "time": {"start": 1755715956427, "stop": 1755715977020, "duration": 20593}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a9eb6eed41e8d978e3f2271d25aeeb9"}, {"name": "test_set_compatibility_mode", "children": [{"name": "测试set compatibility mode返回正确的不支持响应", "uid": "b76067150f1277eb", "parentUid": "71f9d2696419dde9e2fbc887cd35352f", "status": "passed", "time": {"start": 1755715991624, "stop": 1755716012593, "duration": 20969}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "71f9d2696419dde9e2fbc887cd35352f"}, {"name": "test_set_cover_screen_apps", "children": [{"name": "测试set cover screen apps返回正确的不支持响应", "uid": "ce3a29aa80ff9d13", "parentUid": "3e469638b07e0f748f137f566308ee39", "status": "passed", "time": {"start": 1755716027137, "stop": 1755716048487, "duration": 21350}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "3e469638b07e0f748f137f566308ee39"}, {"name": "test_set_customized_cover_screen", "children": [{"name": "测试set customized cover screen返回正确的不支持响应", "uid": "ccb377177100fa65", "parentUid": "681063f39b4b3d4670516dc945bc7e8f", "status": "passed", "time": {"start": 1755716063161, "stop": 1755716083985, "duration": 20824}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "681063f39b4b3d4670516dc945bc7e8f"}, {"name": "test_set_date_time", "children": [{"name": "测试set date & time返回正确的不支持响应", "uid": "3bf62dda522e8ea6", "parentUid": "55afb5474f6367c035d3c3b4c5aa4a0e", "status": "passed", "time": {"start": 1755716098434, "stop": 1755716119033, "duration": 20599}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "55afb5474f6367c035d3c3b4c5aa4a0e"}, {"name": "test_set_edge_mistouch_prevention", "children": [{"name": "测试set edge mistouch prevention返回正确的不支持响应", "uid": "8f4ec68a68e1e48d", "parentUid": "d063298b5e7b7ba1dd78f28933196633", "status": "passed", "time": {"start": 1755716133912, "stop": 1755716155214, "duration": 21302}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d063298b5e7b7ba1dd78f28933196633"}, {"name": "test_set_flex_still_mode", "children": [{"name": "测试set flex-still mode返回正确的不支持响应", "uid": "e56a638a2fdc8665", "parentUid": "fb37e27acd491ebde3cb6675b3ff4b33", "status": "passed", "time": {"start": 1755716169915, "stop": 1755716191439, "duration": 21524}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fb37e27acd491ebde3cb6675b3ff4b33"}, {"name": "test_set_flip_case_feature", "children": [{"name": "测试set flip case feature返回正确的不支持响应", "uid": "6f2361cbbbff8b05", "parentUid": "d093123b5193b9aead35dd1996c7cd1a", "status": "passed", "time": {"start": 1755716205906, "stop": 1755716226591, "duration": 20685}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d093123b5193b9aead35dd1996c7cd1a"}, {"name": "test_set_floating_windows", "children": [{"name": "测试set floating windows返回正确的不支持响应", "uid": "d6477c3a823b2561", "parentUid": "260060301e90f5edbed593882a5fec0d", "status": "passed", "time": {"start": 1755716241228, "stop": 1755716262243, "duration": 21015}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "260060301e90f5edbed593882a5fec0d"}, {"name": "test_set_folding_screen_zone", "children": [{"name": "测试set folding screen zone返回正确的不支持响应", "uid": "488bf54883b7334d", "parentUid": "d7d6eb8f64580ac8b8fb024a954198fa", "status": "passed", "time": {"start": 1755716276958, "stop": 1755716297527, "duration": 20569}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d7d6eb8f64580ac8b8fb024a954198fa"}, {"name": "test_set_font_size", "children": [{"name": "测试set font size返回正确的不支持响应", "uid": "16b942322a4ba58", "parentUid": "740d4f9856207354991fc052a0633f21", "status": "passed", "time": {"start": 1755716312255, "stop": 1755716332960, "duration": 20705}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "740d4f9856207354991fc052a0633f21"}, {"name": "test_set_gesture_navigation", "children": [{"name": "测试set gesture navigation返回正确的不支持响应", "uid": "cc141b0344339aa1", "parentUid": "47320f9ee2ac872ac9be9e52d1af28d8", "status": "passed", "time": {"start": 1755716347825, "stop": 1755716374010, "duration": 26185}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "47320f9ee2ac872ac9be9e52d1af28d8"}, {"name": "test_set_languages", "children": [{"name": "测试set languages返回正确的不支持响应", "uid": "7988511e8b72ea8", "parentUid": "46ab090271dad35a2f22e1aa891d167d", "status": "passed", "time": {"start": 1755716388567, "stop": 1755716409142, "duration": 20575}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "46ab090271dad35a2f22e1aa891d167d"}, {"name": "test_set_lockscreen_passwords", "children": [{"name": "测试set lockscreen passwords返回正确的不支持响应", "uid": "9469bcba4556be7c", "parentUid": "1ebb1fb5844374f960b3ecd76cddec92", "status": "passed", "time": {"start": 1755716423585, "stop": 1755716444442, "duration": 20857}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1ebb1fb5844374f960b3ecd76cddec92"}, {"name": "test_set_my_fonts", "children": [{"name": "测试set my fonts返回正确的不支持响应", "uid": "ee2ca97846776ba6", "parentUid": "c63b678cbbe7de452848a070298e3fab", "status": "passed", "time": {"start": 1755716459388, "stop": 1755716480480, "duration": 21092}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c63b678cbbe7de452848a070298e3fab"}, {"name": "test_set_my_themes", "children": [{"name": "测试set my themes返回正确的不支持响应", "uid": "1eee0185c02a2d72", "parentUid": "452ed970a52e7ee9049bbdb424f9b57d", "status": "passed", "time": {"start": 1755716495654, "stop": 1755716516984, "duration": 21330}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "452ed970a52e7ee9049bbdb424f9b57d"}, {"name": "test_set_nfc_tag", "children": [{"name": "测试set nfc tag", "uid": "70049df9715fc24f", "parentUid": "85fdaa0cd8fa013cf340b4e7ee2dfa5c", "status": "passed", "time": {"start": 1755716531741, "stop": 1755716560064, "duration": 28323}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "85fdaa0cd8fa013cf340b4e7ee2dfa5c"}, {"name": "test_set_off_a_firework", "children": [{"name": "测试set off a firework能正常执行", "uid": "6fbccee76b8de3ce", "parentUid": "bd299436c9a75170d414625cc2cf2562", "status": "passed", "time": {"start": 1755716575019, "stop": 1755716598284, "duration": 23265}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "bd299436c9a75170d414625cc2cf2562"}, {"name": "test_set_parallel_windows", "children": [{"name": "测试set parallel windows返回正确的不支持响应", "uid": "a2ff278134401cef", "parentUid": "b7b2179becb10c93603c2fcce15e0006", "status": "passed", "time": {"start": 1755716613470, "stop": 1755716634162, "duration": 20692}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "b7b2179becb10c93603c2fcce15e0006"}, {"name": "test_set_personal_hotspot", "children": [{"name": "测试set personal hotspot返回正确的不支持响应", "uid": "be5325f87bc04cd5", "parentUid": "4fed48034322a5d5119c937240c4aa77", "status": "passed", "time": {"start": 1755716649188, "stop": 1755716669905, "duration": 20717}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4fed48034322a5d5119c937240c4aa77"}, {"name": "test_set_phantom_v_pen", "children": [{"name": "测试set phantom v pen返回正确的不支持响应", "uid": "855de268ec29413f", "parentUid": "7b8efe12e2cc4a1e4fec01a67e1ead0d", "status": "passed", "time": {"start": 1755716684678, "stop": 1755716705924, "duration": 21246}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7b8efe12e2cc4a1e4fec01a67e1ead0d"}, {"name": "test_set_phone_number", "children": [{"name": "测试set phone number返回正确的不支持响应", "uid": "c6b5a4591b5250a3", "parentUid": "f0d8d3a4b043370c607b0bb64b8df1bd", "status": "passed", "time": {"start": 1755716720450, "stop": 1755716740617, "duration": 20167}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f0d8d3a4b043370c607b0bb64b8df1bd"}, {"name": "test_set_scheduled_power_on_off_and_restart", "children": [{"name": "测试set scheduled power on/off and restart返回正确的不支持响应", "uid": "a3129dbca1ab87c5", "parentUid": "2095cb76657298bca8aa08a5ddc4452c", "status": "passed", "time": {"start": 1755716755460, "stop": 1755716776279, "duration": 20819}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "2095cb76657298bca8aa08a5ddc4452c"}, {"name": "test_set_screen_refresh_rate", "children": [{"name": "测试set screen refresh rate返回正确的不支持响应", "uid": "9c3a90b4e5bc21e1", "parentUid": "d9cca6f6b44bb5f9a4608c3db7a6bb1b", "status": "passed", "time": {"start": 1755716791351, "stop": 1755716812407, "duration": 21056}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d9cca6f6b44bb5f9a4608c3db7a6bb1b"}, {"name": "test_set_screen_relay", "children": [{"name": "测试set screen relay返回正确的不支持响应", "uid": "bd37238f23d42cc1", "parentUid": "66dc5bdcc3e27c2ae07a1536c41488cd", "status": "passed", "time": {"start": 1755716827173, "stop": 1755716848310, "duration": 21137}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "66dc5bdcc3e27c2ae07a1536c41488cd"}, {"name": "test_set_screen_timeout", "children": [{"name": "测试set screen timeout返回正确的不支持响应", "uid": "903782cc98f3e172", "parentUid": "efb5e97f214d22c0692101b1024ecde2", "status": "passed", "time": {"start": 1755716863186, "stop": 1755716884039, "duration": 20853}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "efb5e97f214d22c0692101b1024ecde2"}, {"name": "test_set_screen_to_minimum_brightness", "children": [{"name": "测试set screen to minimum brightness返回正确的不支持响应", "uid": "91b05fae09e97bb2", "parentUid": "e1c10086104522b2fbc3cd552709a125", "status": "passed", "time": {"start": 1755716899062, "stop": 1755716919863, "duration": 20801}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e1c10086104522b2fbc3cd552709a125"}, {"name": "test_set_sim_ringtone", "children": [{"name": "测试set sim1 ringtone返回正确的不支持响应", "uid": "bf86f908019b6fe4", "parentUid": "02b906bfcb368697dfb809905922e9cb", "status": "passed", "time": {"start": 1755716934664, "stop": 1755716955823, "duration": 21159}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "02b906bfcb368697dfb809905922e9cb"}, {"name": "test_set_smart_hub", "children": [{"name": "测试set smart hub返回正确的不支持响应", "uid": "39a8d0fd53a2c6ce", "parentUid": "1b885e8f75c6cb8827815cfc72755c55", "status": "passed", "time": {"start": 1755716970970, "stop": 1755716991444, "duration": 20474}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1b885e8f75c6cb8827815cfc72755c55"}, {"name": "test_set_smart_panel", "children": [{"name": "测试set smart panel返回正确的不支持响应", "uid": "2b7fde4df860431e", "parentUid": "116ddf2803a2d3fa8977f8f2b43e59a3", "status": "passed", "time": {"start": 1755717005889, "stop": 1755717026585, "duration": 20696}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "116ddf2803a2d3fa8977f8f2b43e59a3"}, {"name": "test_set_special_function", "children": [{"name": "测试set special function返回正确的不支持响应", "uid": "630bd3a65e5c550f", "parentUid": "1cdef3d89962b3a41ead4e6a44623984", "status": "passed", "time": {"start": 1755717041701, "stop": 1755717062984, "duration": 21283}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1cdef3d89962b3a41ead4e6a44623984"}, {"name": "test_set_split_screen_apps", "children": [{"name": "测试set split-screen apps返回正确的不支持响应", "uid": "63f252b21b68ebf8", "parentUid": "df5405aa0bd507fb6dc22f263be7dfef", "status": "passed", "time": {"start": 1755717078070, "stop": 1755717098532, "duration": 20462}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "df5405aa0bd507fb6dc22f263be7dfef"}, {"name": "test_set_timer", "children": [{"name": "测试set timer", "uid": "3c088f68985d802", "parentUid": "8fcfb88078ce21426d07ad83897d1dba", "status": "passed", "time": {"start": 1755717113523, "stop": 1755717138452, "duration": 24929}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8fcfb88078ce21426d07ad83897d1dba"}, {"name": "test_set_timezone", "children": [{"name": "测试set timezone返回正确的不支持响应", "uid": "1b08313dfe29e27", "parentUid": "9e7c120412bd7e0f730e61377785fa8a", "status": "passed", "time": {"start": 1755717153273, "stop": 1755717174591, "duration": 21318}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9e7c120412bd7e0f730e61377785fa8a"}, {"name": "test_set_ultra_power_saving", "children": [{"name": "测试set ultra power saving返回正确的不支持响应", "uid": "6b2b3674508b58e", "parentUid": "5cae1c2c93d377af57591ba886a396b7", "status": "passed", "time": {"start": 1755717189600, "stop": 1755717210441, "duration": 20841}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cae1c2c93d377af57591ba886a396b7"}, {"name": "test_start_boosting_phone", "children": [{"name": "测试start boosting phone能正常执行", "uid": "46f8645a9e56b76a", "parentUid": "c0500ddbbfde32ada0f1ae6d634b7947", "status": "passed", "time": {"start": 1755717225156, "stop": 1755717245928, "duration": 20772}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c0500ddbbfde32ada0f1ae6d634b7947"}, {"name": "test_start_running", "children": [{"name": "测试start running能正常执行", "uid": "791c94de0efcf34", "parentUid": "8fdaf0f924e8731506064256cfd9d49e", "status": "passed", "time": {"start": 1755717260698, "stop": 1755717286511, "duration": 25813}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8fdaf0f924e8731506064256cfd9d49e"}, {"name": "test_start_walking", "children": [{"name": "测试start walking能正常执行", "uid": "7b245ac8e527449f", "parentUid": "4a8c9e4aef8cc53d5214b16790f29ecf", "status": "skipped", "time": {"start": 1755717287853, "stop": 1755717287853, "duration": 0}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["@pytest.mark.skip(reason='start walking命令都会开Health应用，才能执行，无法通用化')", "smoke"]}], "uid": "4a8c9e4aef8cc53d5214b16790f29ecf"}, {"name": "test_summarize_content_on_this_page", "children": [{"name": "测试summarize content on this page", "uid": "2e55777fd13a57c0", "parentUid": "55ce916af0728638845a795948e38c67", "status": "passed", "time": {"start": 1755717300879, "stop": 1755717321081, "duration": 20202}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "55ce916af0728638845a795948e38c67"}, {"name": "test_summarize_what_i_m_reading", "children": [{"name": "测试Summarize what I'm reading", "uid": "5da2150a89012bb7", "parentUid": "c866cefd3cd73292a41110064d69746d", "status": "passed", "time": {"start": 1755717335502, "stop": 1755717356195, "duration": 20693}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c866cefd3cd73292a41110064d69746d"}, {"name": "test_switch_to_davido_voice", "children": [{"name": "测试Switch to davido voice能正常执行", "uid": "69c78d8d1530af12", "parentUid": "345255e1a4eaa1b4c4a073d2355fd91c", "status": "passed", "time": {"start": 1755717370412, "stop": 1755717390704, "duration": 20292}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "345255e1a4eaa1b4c4a073d2355fd91c"}, {"name": "test_switch_to_equilibrium_mode", "children": [{"name": "测试switch to equilibrium mode返回正确的不支持响应", "uid": "92b833200938e00b", "parentUid": "eaf66c431b1bc4cdb440f63e804d5b7f", "status": "passed", "time": {"start": 1755717404869, "stop": 1755717425131, "duration": 20262}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "eaf66c431b1bc4cdb440f63e804d5b7f"}, {"name": "test_switch_to_performance_mode", "children": [{"name": "测试switch to performance mode返回正确的不支持响应", "uid": "9d3cea02739dbf7e", "parentUid": "9c64338b2e6a23f5945b6c45a6e8988f", "status": "passed", "time": {"start": 1755717439710, "stop": 1755717460594, "duration": 20884}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9c64338b2e6a23f5945b6c45a6e8988f"}, {"name": "test_switch_to_power_saving_mode", "children": [{"name": "测试switch to power saving mode返回正确的不支持响应", "uid": "2e076a866aa19b91", "parentUid": "0a89b8197df33175fe755b5815c5229b", "status": "passed", "time": {"start": 1755717474882, "stop": 1755717495286, "duration": 20404}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "0a89b8197df33175fe755b5815c5229b"}, {"name": "test_switching_charging_speed", "children": [{"name": "测试switching charging speed能正常执行", "uid": "52b1168d520f1090", "parentUid": "556ad59feb219cf0e64a09e0c312ef81", "status": "passed", "time": {"start": 1755717509854, "stop": 1755717530141, "duration": 20287}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "556ad59feb219cf0e64a09e0c312ef81"}, {"name": "test_take_notes", "children": [{"name": "测试take notes能正常执行", "uid": "64e2ef5736676f9d", "parentUid": "7509a983d8243a8effb8a61f465b5c1a", "status": "failed", "time": {"start": 1755717544335, "stop": 1755717569297, "duration": 24962}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "7509a983d8243a8effb8a61f465b5c1a"}, {"name": "test_tell_me_a_joke", "children": [{"name": "测试tell me a joke能正常执行", "uid": "a1f27db92dd979ce", "parentUid": "8b698828f35d1b5e0ecb9b450dc40242", "status": "failed", "time": {"start": 1755717583953, "stop": 1755717604579, "duration": 20626}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8b698828f35d1b5e0ecb9b450dc40242"}, {"name": "test_tell_me_joke", "children": [{"name": "测试tell me joke能正常执行", "uid": "710dc5b63057da8e", "parentUid": "cb157e6b5cd8cba7c4577e10e0dd7c22", "status": "passed", "time": {"start": 1755717619004, "stop": 1755717639353, "duration": 20349}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "cb157e6b5cd8cba7c4577e10e0dd7c22"}, {"name": "test_the_mobile_phone_is_very_hot", "children": [{"name": "测试the mobile phone is very hot", "uid": "f0918076a332776f", "parentUid": "a4c6d4cdd9b5c797269ad542918d8253", "status": "passed", "time": {"start": 1755717653718, "stop": 1755717676679, "duration": 22961}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a4c6d4cdd9b5c797269ad542918d8253"}, {"name": "test_there_are_many_yellow_sunflowers_on_the_ground", "children": [{"name": "测试there are many yellow sunflowers on the ground", "uid": "b3ac3f5aab7b53bc", "parentUid": "c87417891e4cf964c74e3c52135f1012", "status": "passed", "time": {"start": 1755717691603, "stop": 1755717712579, "duration": 20976}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c87417891e4cf964c74e3c52135f1012"}, {"name": "test_there_are_transparent_glowing_multicolored_soap_bubbles_around_it", "children": [{"name": "测试There are transparent, glowing multicolored soap bubbles around it", "uid": "cead85b9790eb619", "parentUid": "a977b51920eca060a5b122c5aff6cc53", "status": "passed", "time": {"start": 1755717726216, "stop": 1755717746762, "duration": 20546}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a977b51920eca060a5b122c5aff6cc53"}, {"name": "test_there_is_a_colorful_butterfly_beside_it", "children": [{"name": "测试there is a colorful butterfly beside it", "uid": "a247510454373d34", "parentUid": "8c6aaf4045a81b3320a15d452b5d7e2f", "status": "passed", "time": {"start": 1755717761136, "stop": 1755717781768, "duration": 20632}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "8c6aaf4045a81b3320a15d452b5d7e2f"}, {"name": "test_three_little_pigs", "children": [{"name": "测试Three Little Pigs", "uid": "cf0c2892ea9348fd", "parentUid": "e448ef7c7bd02012da4b9d380ad338b5", "status": "passed", "time": {"start": 1755717796370, "stop": 1755717817717, "duration": 21347}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "e448ef7c7bd02012da4b9d380ad338b5"}, {"name": "test_turn_off_driving_mode", "children": [{"name": "测试turn off driving mode返回正确的不支持响应", "uid": "b50ecabc6cc24973", "parentUid": "07d91c6eb72b9bf62af23569018b6ed9", "status": "passed", "time": {"start": 1755717832334, "stop": 1755717852489, "duration": 20155}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "07d91c6eb72b9bf62af23569018b6ed9"}, {"name": "test_turn_off_show_battery_percentage", "children": [{"name": "测试turn off show battery percentage返回正确的不支持响应", "uid": "e0bcd67d09add88c", "parentUid": "1da9f8360bfb0009b7bbba3b558b5ad6", "status": "passed", "time": {"start": 1755717866874, "stop": 1755717887416, "duration": 20542}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1da9f8360bfb0009b7bbba3b558b5ad6"}, {"name": "test_turn_on_driving_mode", "children": [{"name": "测试turn on driving mode返回正确的不支持响应", "uid": "9e55628222e59320", "parentUid": "fd2d2a21ef2aa878399c88b3d0e934ba", "status": "passed", "time": {"start": 1755717901535, "stop": 1755717922341, "duration": 20806}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "fd2d2a21ef2aa878399c88b3d0e934ba"}, {"name": "test_turn_on_high_brightness_mode", "children": [{"name": "测试turn on high brightness mode返回正确的不支持响应", "uid": "2228f510d1fec9a0", "parentUid": "49b44a51671f9fc053854051c80f7c70", "status": "passed", "time": {"start": 1755717936863, "stop": 1755717957311, "duration": 20448}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "49b44a51671f9fc053854051c80f7c70"}, {"name": "test_turn_on_show_battery_percentage", "children": [{"name": "测试turn on show battery percentage返回正确的不支持响应", "uid": "c1d759363b985608", "parentUid": "838b34c8fa65f970357a94fb9eaf0dcc", "status": "passed", "time": {"start": 1755717971969, "stop": 1755717992485, "duration": 20516}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "838b34c8fa65f970357a94fb9eaf0dcc"}, {"name": "test_vedio_call_number_by_whatsapp", "children": [{"name": "测试vedio call number by whatsapp能正常执行", "uid": "c5bdc9fc6c1955b3", "parentUid": "9d137f0f051ce9331aca868c940ab405", "status": "failed", "time": {"start": 1755718006489, "stop": 1755718035885, "duration": 29396}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9d137f0f051ce9331aca868c940ab405"}, {"name": "test_view_in_notebook", "children": [{"name": "测试view in notebook", "uid": "ace97ecbc38601f0", "parentUid": "4a568b6f4b1988b1b280aac795977cf4", "status": "passed", "time": {"start": 1755718050591, "stop": 1755718078306, "duration": 27715}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4a568b6f4b1988b1b280aac795977cf4"}, {"name": "test_voice_setting_page", "children": [{"name": "测试Voice setting page返回正确的不支持响应", "uid": "c48adfa7289fdf04", "parentUid": "c2e243e17e6068d0d67070f2c20e8486", "status": "passed", "time": {"start": 1755718092438, "stop": 1755718123633, "duration": 31195}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "c2e243e17e6068d0d67070f2c20e8486"}, {"name": "test_what_date_is_it", "children": [{"name": "测试what date is it能正常执行", "uid": "3eb7f8540804d8d9", "parentUid": "91a4dbcca261fa96c4858bdb888ab618", "status": "passed", "time": {"start": 1755718138197, "stop": 1755718158627, "duration": 20430}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "91a4dbcca261fa96c4858bdb888ab618"}, {"name": "test_what_is_the_weather_today", "children": [{"name": "测试what is the weather today能正常执行", "uid": "4e7f873cdb9a1a05", "parentUid": "f9448cabe248d24cefaab6116ebbe403", "status": "failed", "time": {"start": 1755718173048, "stop": 1755718199761, "duration": 26713}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "f9448cabe248d24cefaab6116ebbe403"}, {"name": "test_what_s_the_date_today", "children": [{"name": "测试what's the date today", "uid": "58260e42e810e85e", "parentUid": "1e7565034ebb8f9de8885edeada8be54", "status": "passed", "time": {"start": 1755718214394, "stop": 1755718235286, "duration": 20892}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "1e7565034ebb8f9de8885edeada8be54"}, {"name": "test_what_s_your_name", "children": [{"name": "测试what's your name", "uid": "e9241c09bf9f876", "parentUid": "a1b78446c4fd3cc42f6e7a24189e01e4", "status": "passed", "time": {"start": 1755718249894, "stop": 1755718270338, "duration": 20444}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "a1b78446c4fd3cc42f6e7a24189e01e4"}, {"name": "test_what_time_is_it", "children": [{"name": "测试what time is it能正常执行", "uid": "be63ad83ca192ad0", "parentUid": "9b2aff3b0a6e2514bd870f8249da94f2", "status": "passed", "time": {"start": 1755718284705, "stop": 1755718306600, "duration": 21895}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "9b2aff3b0a6e2514bd870f8249da94f2"}, {"name": "test_what_time_is_it_in_china", "children": [{"name": "测试what time is it in china能正常执行", "uid": "de96da566e06249f", "parentUid": "4708be60f0bd03b2f9d73a1d9a3dbd08", "status": "passed", "time": {"start": 1755718320894, "stop": 1755718343451, "duration": 22557}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "4708be60f0bd03b2f9d73a1d9a3dbd08"}, {"name": "test_what_time_is_it_in_london", "children": [{"name": "测试what time is it in London能正常执行", "uid": "5b51fa563fcca0d", "parentUid": "73e8c462f6a0b1457c865212c98a100e", "status": "passed", "time": {"start": 1755718358035, "stop": 1755718379739, "duration": 21704}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "73e8c462f6a0b1457c865212c98a100e"}, {"name": "test_where_is_my_car", "children": [{"name": "测试where is my car能正常执行", "uid": "1142c6b537107ee2", "parentUid": "d24794db3e8c3ed208a348e4c4a1e4a0", "status": "passed", "time": {"start": 1755718394394, "stop": 1755718414764, "duration": 20370}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "d24794db3e8c3ed208a348e4c4a1e4a0"}, {"name": "test_where_s_my_car", "children": [{"name": "测试where`s my car能正常执行", "uid": "841dcfa4d7cc0b8a", "parentUid": "5cddcd62a6a24cfd23af0bb73d272e2d", "status": "passed", "time": {"start": 1755718428825, "stop": 1755718449822, "duration": 20997}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "5cddcd62a6a24cfd23af0bb73d272e2d"}, {"name": "test_yandex_eats", "children": [{"name": "测试yandex eats返回正确的不支持响应", "uid": "d26e034db66f3bb1", "parentUid": "ffa74429a7c2fa0151c7e7ef27b25ecb", "status": "passed", "time": {"start": 1755718464076, "stop": 1755718484775, "duration": 20699}, "flaky": false, "newFailed": false, "newPassed": false, "newBroken": false, "retriesCount": 0, "retriesStatusChange": false, "parameters": [], "tags": ["smoke"]}], "uid": "ffa74429a7c2fa0151c7e7ef27b25ecb"}], "uid": "e416b8e9994852e8ed797dec283160f6"}], "uid": "testcases.test_ella"}]}