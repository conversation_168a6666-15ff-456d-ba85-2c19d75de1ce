[{"uid": "33c23c78138d1c18", "name": "测试help me write an email能正常执行", "time": {"start": 1755713225256, "stop": 1755713248389, "duration": 23133}, "status": "passed", "severity": "critical"}, {"uid": "b9a2b43807e7424c", "name": "测试Switch to Barrage Notification能正常执行", "time": {"start": 1755707930297, "stop": 1755707950934, "duration": 20637}, "status": "passed", "severity": "critical"}, {"uid": "857baf7b1c9bdf69", "name": "测试min brightness能正常执行", "time": {"start": 1755706735522, "stop": 1755706757517, "duration": 21995}, "status": "passed", "severity": "critical"}, {"uid": "b46b15d39e0b3b36", "name": "测试driving mode返回正确的不支持响应", "time": {"start": 1755711860041, "stop": 1755711880454, "duration": 20413}, "status": "passed", "severity": "normal"}, {"uid": "2ef78000037a1acc", "name": "测试help me take a screenshot能正常执行", "time": {"start": 1755706197680, "stop": 1755706221962, "duration": 24282}, "status": "passed", "severity": "critical"}, {"uid": "46bca85c836641b0", "name": "测试set notifications volume to 50能正常执行", "time": {"start": 1755707350893, "stop": 1755707372785, "duration": 21892}, "status": "passed", "severity": "critical"}, {"uid": "630bd3a65e5c550f", "name": "测试set special function返回正确的不支持响应", "time": {"start": 1755717041701, "stop": 1755717062984, "duration": 21283}, "status": "passed", "severity": "normal"}, {"uid": "12d1457f5f2c6ee2", "name": "测试play taylor swift‘s song love story", "time": {"start": 1755715149162, "stop": 1755715181119, "duration": 31957}, "status": "passed", "severity": "critical"}, {"uid": "3c088f68985d802", "name": "测试set timer", "time": {"start": 1755717113523, "stop": 1755717138452, "duration": 24929}, "status": "passed", "severity": "critical"}, {"uid": "137d3ab7ca2b4635", "name": "测试A little raccoon is walking on the forest meadow, surrounded by a row of green bamboo groves, with layers of high mountains shrouded in clouds and mist in the distance", "time": {"start": 1755710378620, "stop": 1755710401319, "duration": 22699}, "status": "passed", "severity": "critical"}, {"uid": "e9241c09bf9f876", "name": "测试what's your name", "time": {"start": 1755718249894, "stop": 1755718270338, "duration": 20444}, "status": "passed", "severity": "critical"}, {"uid": "5a47ad7486e4ec29", "name": "测试what's the weather today?能正常执行", "time": {"start": 1755704541401, "stop": 1755704569475, "duration": 28074}, "status": "failed", "severity": "critical"}, {"uid": "dd6625e7baf2349d", "name": "测试turn on the 7AM alarm", "time": {"start": 1755709149214, "stop": 1755709170312, "duration": 21098}, "status": "passed", "severity": "critical"}, {"uid": "d9e2a42bc660c8a2", "name": "测试What languages do you support能正常执行", "time": {"start": 1755704315163, "stop": 1755704335969, "duration": 20806}, "status": "passed", "severity": "critical"}, {"uid": "caa814f725fc2a01", "name": "测试how's the weather today in shanghai能正常执行", "time": {"start": 1755702515608, "stop": 1755702543010, "duration": 27402}, "status": "passed", "severity": "critical"}, {"uid": "4b4ca06394093464", "name": "测试turn on light theme能正常执行", "time": {"start": 1755708970871, "stop": 1755708991635, "duration": 20764}, "status": "passed", "severity": "critical"}, {"uid": "27f56deb688575b7", "name": "测试continue music能正常执行", "time": {"start": 1755700385328, "stop": 1755700406429, "duration": 21101}, "status": "passed", "severity": "critical"}, {"uid": "24b291e1c219b3ab", "name": "测试order a takeaway能正常执行", "time": {"start": 1755710021144, "stop": 1755710041268, "duration": 20124}, "status": "failed", "severity": "critical"}, {"uid": "cead85b9790eb619", "name": "测试There are transparent, glowing multicolored soap bubbles around it", "time": {"start": 1755717726216, "stop": 1755717746762, "duration": 20546}, "status": "passed", "severity": "critical"}, {"uid": "a0cc9190d48976f5", "name": "测试increase the volume to the maximun能正常执行", "time": {"start": 1755706345675, "stop": 1755706366929, "duration": 21254}, "status": "passed", "severity": "critical"}, {"uid": "89017fbed8e16c62", "name": "测试appeler maman能正常执行", "time": {"start": 1755701800427, "stop": 1755701820824, "duration": 20397}, "status": "passed", "severity": "critical"}, {"uid": "e121af0e43898b5", "name": "测试play music by yandex music", "time": {"start": 1755703271698, "stop": 1755703294462, "duration": 22764}, "status": "passed", "severity": "critical"}, {"uid": "d683cbee37868329", "name": "测试Generate a cartoon-style puppy image for me with a 4:3 aspect ratio", "time": {"start": 1755712406479, "stop": 1755712426861, "duration": 20382}, "status": "passed", "severity": "critical"}, {"uid": "9d3cea02739dbf7e", "name": "测试switch to performance mode返回正确的不支持响应", "time": {"start": 1755717439710, "stop": 1755717460594, "duration": 20884}, "status": "passed", "severity": "normal"}, {"uid": "6463e34a97199c46", "name": "测试restart my phone能正常执行", "time": {"start": 1755715568959, "stop": 1755715568959, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "738b70d304f66c90", "name": "测试turn on light theme能正常执行", "time": {"start": 1755709006278, "stop": 1755709026560, "duration": 20282}, "status": "passed", "severity": "critical"}, {"uid": "3afa7a07e2667b4b", "name": "测试adjustment the brightness to minimun能正常执行", "time": {"start": 1755705670212, "stop": 1755705691592, "duration": 21380}, "status": "passed", "severity": "critical"}, {"uid": "c18c29d4fbf8459a", "name": "测试turn down alarm clock volume", "time": {"start": 1755708433297, "stop": 1755708454918, "duration": 21621}, "status": "passed", "severity": "critical"}, {"uid": "84e86790fa4b5099", "name": "测试make a call by whatsapp能正常执行", "time": {"start": 1755714165311, "stop": 1755714194613, "duration": 29302}, "status": "failed", "severity": "critical"}, {"uid": "3cf2f607644999ee", "name": "测试open camera", "time": {"start": 1755714565982, "stop": 1755714606300, "duration": 40318}, "status": "passed", "severity": "critical"}, {"uid": "e3084c3fc9a93c05", "name": "测试Search for addresses on the screen能正常执行", "time": {"start": 1755715619742, "stop": 1755715640804, "duration": 21062}, "status": "passed", "severity": "critical"}, {"uid": "e218cc5b5c759e28", "name": "测试phone boost能正常执行", "time": {"start": 1755701110195, "stop": 1755701131153, "duration": 20958}, "status": "passed", "severity": "critical"}, {"uid": "e2ca15307ddb2dc0", "name": "测试play news", "time": {"start": 1755703393400, "stop": 1755703416211, "duration": 22811}, "status": "passed", "severity": "critical"}, {"uid": "d60c330ba8cd6757", "name": "continue  screen recording能正常执行", "time": {"start": 1755707675377, "stop": 1755707696328, "duration": 20951}, "status": "failed", "severity": "critical"}, {"uid": "49c7a831e04edfb5", "name": "测试enable all ai magic box features返回正确的不支持响应", "time": {"start": 1755711930713, "stop": 1755711951175, "duration": 20462}, "status": "passed", "severity": "normal"}, {"uid": "58260e42e810e85e", "name": "测试what's the date today", "time": {"start": 1755718214394, "stop": 1755718235286, "duration": 20892}, "status": "passed", "severity": "critical"}, {"uid": "ee35e6bf1fc33f7e", "name": "测试record audio for 5 seconds能正常执行", "time": {"start": 1755701462783, "stop": 1755701489976, "duration": 27193}, "status": "failed", "severity": "critical"}, {"uid": "a2ff278134401cef", "name": "测试set parallel windows返回正确的不支持响应", "time": {"start": 1755716613470, "stop": 1755716634162, "duration": 20692}, "status": "passed", "severity": "normal"}, {"uid": "dd4f0642dceeb932", "name": "测试switch magic voice to <PERSON><PERSON>能正常执行", "time": {"start": 1755707895286, "stop": 1755707915999, "duration": 20713}, "status": "passed", "severity": "critical"}, {"uid": "1dc62f6cb6eaeb3a", "name": "测试previous song能正常执行", "time": {"start": 1755703469271, "stop": 1755703489648, "duration": 20377}, "status": "passed", "severity": "critical"}, {"uid": "68c83f240cb84ef1", "name": "测试hi能正常执行", "time": {"start": 1755702359010, "stop": 1755702381566, "duration": 22556}, "status": "passed", "severity": "critical"}, {"uid": "dc18d3af30b26f69", "name": "测试turn up alarm clock volume", "time": {"start": 1755709319712, "stop": 1755709340963, "duration": 21251}, "status": "passed", "severity": "critical"}, {"uid": "922bf33b821ac18f", "name": "测试change (female/tone name) voice能正常执行", "time": {"start": 1755710651429, "stop": 1755710672906, "duration": 21477}, "status": "passed", "severity": "critical"}, {"uid": "63e40151836706cb", "name": "测试Help me generate a 3D rendered picture of a Song-style palace surrounded by auspicious clouds and with delicate colors", "time": {"start": 1755712874437, "stop": 1755712894647, "duration": 20210}, "status": "passed", "severity": "critical"}, {"uid": "5524ec83adbefa12", "name": "测试close bluetooth能正常执行", "time": {"start": 1755705900799, "stop": 1755705923362, "duration": 22563}, "status": "passed", "severity": "critical"}, {"uid": "17d2a0a003d6d160", "name": "测试disable call rejection返回正确的不支持响应", "time": {"start": 1755711445164, "stop": 1755711475678, "duration": 30514}, "status": "passed", "severity": "normal"}, {"uid": "32cde91effcb28af", "name": "测试A cute little boy is skiing", "time": {"start": 1755710272924, "stop": 1755710293558, "duration": 20634}, "status": "passed", "severity": "critical"}, {"uid": "9834e78b2b998f9d", "name": "测试open countdown能正常执行", "time": {"start": 1755700798110, "stop": 1755700819231, "duration": 21121}, "status": "passed", "severity": "critical"}, {"uid": "4696dac000e64935", "name": "测试search my gallery for food pictures能正常执行", "time": {"start": 1755703578511, "stop": 1755703603431, "duration": 24920}, "status": "passed", "severity": "critical"}, {"uid": "93fccfa236b74211", "name": "测试order a burger返回正确的不支持响应", "time": {"start": 1755714869937, "stop": 1755714890353, "duration": 20416}, "status": "failed", "severity": "normal"}, {"uid": "4f7337a8b7498257", "name": "测试help me generate a picture of green trees in shade and distant mountains in a hazy state", "time": {"start": 1755713155584, "stop": 1755713175967, "duration": 20383}, "status": "passed", "severity": "critical"}, {"uid": "6c5f86b548af54ce", "name": "测试open bt", "time": {"start": 1755706918545, "stop": 1755706939645, "duration": 21100}, "status": "passed", "severity": "critical"}, {"uid": "796644c4e7b3e78e", "name": "测试take a photo能正常执行", "time": {"start": 1755708268047, "stop": 1755708311973, "duration": 43926}, "status": "failed", "severity": "critical"}, {"uid": "e468d6a321e1e20", "name": "测试navigate to shanghai disneyland能正常执行", "time": {"start": 1755709823196, "stop": 1755709851513, "duration": 28317}, "status": "passed", "severity": "critical"}, {"uid": "41c36b3af557b8cf", "name": "测试restart the phone能正常执行", "time": {"start": 1755715568963, "stop": 1755715568963, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "3f6b89daaeb1bc0e", "name": "测试turn up the volume to the max能正常执行", "time": {"start": 1755709463485, "stop": 1755709484276, "duration": 20791}, "status": "passed", "severity": "critical"}, {"uid": "137e5c8f5f4436f9", "name": "测试i wanna be rich能正常执行", "time": {"start": 1755702630224, "stop": 1755702653824, "duration": 23600}, "status": "passed", "severity": "critical"}, {"uid": "c1d759363b985608", "name": "测试turn on show battery percentage返回正确的不支持响应", "time": {"start": 1755717971969, "stop": 1755717992485, "duration": 20516}, "status": "passed", "severity": "normal"}, {"uid": "42e22ad7e13fa77", "name": "测试extend the image能正常执行", "time": {"start": 1755712336003, "stop": 1755712356170, "duration": 20167}, "status": "failed", "severity": "critical"}, {"uid": "45df33e0046924ed", "name": "测试change your language能正常执行", "time": {"start": 1755705741223, "stop": 1755705765810, "duration": 24587}, "status": "passed", "severity": "critical"}, {"uid": "fb609f333dbf8703", "name": "测试disable call on hold返回正确的不支持响应", "time": {"start": 1755700497842, "stop": 1755700527970, "duration": 30128}, "status": "passed", "severity": "normal"}, {"uid": "3602fab948853dc9", "name": "测试take notes on how to build a treehouse能正常执行", "time": {"start": 1755704088367, "stop": 1755704113810, "duration": 25443}, "status": "passed", "severity": "critical"}, {"uid": "46eda86c2db0307b", "name": "测试A cute little girl with long hair, wearing a scarf, a white cotton-padded jacket, and carrying a backpack on her back, has a kitten at her feet and colorful little butterflies fluttering around her", "time": {"start": 1755710307810, "stop": 1755710328714, "duration": 20904}, "status": "failed", "severity": "critical"}, {"uid": "a33f0f1e44b65ad8", "name": "测试call number by whatsapp能正常执行", "time": {"start": 1755710565547, "stop": 1755710594408, "duration": 28861}, "status": "failed", "severity": "critical"}, {"uid": "79a7c43986e8c28a", "name": "测试pause music能正常执行", "time": {"start": 1755701037928, "stop": 1755701059246, "duration": 21318}, "status": "passed", "severity": "critical"}, {"uid": "e43d803ea41dae27", "name": "测试can you give me a coin能正常执行", "time": {"start": 1755701914884, "stop": 1755701938613, "duration": 23729}, "status": "passed", "severity": "critical"}, {"uid": "92b833200938e00b", "name": "测试switch to equilibrium mode返回正确的不支持响应", "time": {"start": 1755717404869, "stop": 1755717425131, "duration": 20262}, "status": "passed", "severity": "normal"}, {"uid": "b0a48258d05596ff", "name": "测试book a flight to paris返回正确的不支持响应", "time": {"start": 1755701835293, "stop": 1755701855687, "duration": 20394}, "status": "broken", "severity": "normal"}, {"uid": "84bf7ebf445383bc", "name": "测试help me take a long screenshot能正常执行", "time": {"start": 1755706158440, "stop": 1755706182603, "duration": 24163}, "status": "passed", "severity": "critical"}, {"uid": "4105641adf8eeed4", "name": "测试change your language to chinese能正常执行", "time": {"start": 1755705767182, "stop": 1755705767182, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "45fadb3dac70f843", "name": "测试video call mom through whatsapp能正常执行", "time": {"start": 1755704199431, "stop": 1755704227984, "duration": 28553}, "status": "failed", "severity": "critical"}, {"uid": "b732fc1153a563f6", "name": "测试max brightness能正常执行", "time": {"start": 1755706491479, "stop": 1755706513206, "duration": 21727}, "status": "passed", "severity": "critical"}, {"uid": "5156b101e511d7c4", "name": "测试change your voice能正常执行", "time": {"start": 1755710721885, "stop": 1755710742518, "duration": 20633}, "status": "passed", "severity": "critical"}, {"uid": "7e0add9ec94ae39b", "name": "测试next music能正常执行", "time": {"start": 1755703008961, "stop": 1755703029423, "duration": 20462}, "status": "passed", "severity": "critical"}, {"uid": "bbb773782dcfa0d8", "name": "测试play video", "time": {"start": 1755715242940, "stop": 1755715270305, "duration": 27365}, "status": "passed", "severity": "critical"}, {"uid": "2053024043889923", "name": "测试play music by VLC", "time": {"start": 1755703149714, "stop": 1755703173141, "duration": 23427}, "status": "passed", "severity": "critical"}, {"uid": "92bd1b35636073c0", "name": "测试make a phone call to 17621905233", "time": {"start": 1755714241421, "stop": 1755714241421, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "71f530986a1c345", "name": "测试wake me up at 7:00 am tomorrow能正常执行", "time": {"start": 1755709498977, "stop": 1755709519819, "duration": 20842}, "status": "failed", "severity": "critical"}, {"uid": "5bfde334bbb93da9", "name": "测试turn on smart reminder能正常执行", "time": {"start": 1755709113798, "stop": 1755709134251, "duration": 20453}, "status": "failed", "severity": "critical"}, {"uid": "a155cce1034230a3", "name": "测试check system update", "time": {"start": 1755711091508, "stop": 1755711112221, "duration": 20713}, "status": "passed", "severity": "critical"}, {"uid": "ce3e4bfb4f1d431d", "name": "测试enable accelerate dialogue返回正确的不支持响应", "time": {"start": 1755711895033, "stop": 1755711916062, "duration": 21029}, "status": "passed", "severity": "normal"}, {"uid": "6f18513442b4a26d", "name": "测试Generate a circular car logo image with a three-pointed star inside the logo", "time": {"start": 1755712441418, "stop": 1755712461689, "duration": 20271}, "status": "passed", "severity": "critical"}, {"uid": "3feaf46a9f9c6a7c", "name": "测试close phonemaster能正常执行", "time": {"start": 1755700350192, "stop": 1755700371063, "duration": 20871}, "status": "passed", "severity": "critical"}, {"uid": "cc141b0344339aa1", "name": "测试set gesture navigation返回正确的不支持响应", "time": {"start": 1755716347825, "stop": 1755716374010, "duration": 26185}, "status": "passed", "severity": "normal"}, {"uid": "5fb1f8cdacb21403", "name": "测试What's the weather like today能正常执行", "time": {"start": 1755704350521, "stop": 1755704378244, "duration": 27723}, "status": "failed", "severity": "critical"}, {"uid": "c6b5a4591b5250a3", "name": "测试set phone number返回正确的不支持响应", "time": {"start": 1755716720450, "stop": 1755716740617, "duration": 20167}, "status": "passed", "severity": "normal"}, {"uid": "f64c738cc7a9bff6", "name": "测试turn up the brightness to the max能正常执行", "time": {"start": 1755709428263, "stop": 1755709448886, "duration": 20623}, "status": "passed", "severity": "critical"}, {"uid": "949ca3c8b711de72", "name": "测试check mobile data balance of sim2返回正确的不支持响应", "time": {"start": 1755710879509, "stop": 1755710900214, "duration": 20705}, "status": "failed", "severity": "normal"}, {"uid": "500f2d3edf3668e6", "name": "测试set the alarm at 9 o'clock on weekends", "time": {"start": 1755707461475, "stop": 1755707482773, "duration": 21298}, "status": "passed", "severity": "critical"}, {"uid": "9e55628222e59320", "name": "测试turn on driving mode返回正确的不支持响应", "time": {"start": 1755717901535, "stop": 1755717922341, "duration": 20806}, "status": "passed", "severity": "normal"}, {"uid": "ccb377177100fa65", "name": "测试set customized cover screen返回正确的不支持响应", "time": {"start": 1755716063161, "stop": 1755716083985, "duration": 20824}, "status": "passed", "severity": "normal"}, {"uid": "ce467c19240b201f", "name": "测试take a selfie能正常执行", "time": {"start": 1755708330448, "stop": 1755708372133, "duration": 41685}, "status": "passed", "severity": "critical"}, {"uid": "a1f27db92dd979ce", "name": "测试tell me a joke能正常执行", "time": {"start": 1755717583953, "stop": 1755717604579, "duration": 20626}, "status": "failed", "severity": "critical"}, {"uid": "fc2a1d7492dfd853", "name": "测试what time is it now能正常执行", "time": {"start": 1755704504441, "stop": 1755704526816, "duration": 22375}, "status": "passed", "severity": "critical"}, {"uid": "cb9480142c2cdbd4", "name": "测试how to set screenshots返回正确的不支持响应", "time": {"start": 1755713336030, "stop": 1755713356947, "duration": 20917}, "status": "passed", "severity": "normal"}, {"uid": "57d7096266b039f9", "name": "测试Summarize what I'm reading能正常执行", "time": {"start": 1755705533332, "stop": 1755705583011, "duration": 49679}, "status": "failed", "severity": "critical"}, {"uid": "f002a91a86cc5d52", "name": "测试search whatsapp for me能正常执行", "time": {"start": 1755715691494, "stop": 1755715718074, "duration": 26580}, "status": "passed", "severity": "critical"}, {"uid": "e08a284bddf2d94", "name": "测试Enable Network Enhancement返回正确的不支持响应", "time": {"start": 1755712122091, "stop": 1755712143242, "duration": 21151}, "status": "passed", "severity": "normal"}, {"uid": "f076e424c2a10ed7", "name": "测试close ella能正常执行", "time": {"start": 1755700251724, "stop": 1755700286265, "duration": 34541}, "status": "passed", "severity": "critical"}, {"uid": "fe7678a6c04e5be5", "name": "测试play jay chou's music by spotify", "time": {"start": 1755701246247, "stop": 1755701269209, "duration": 22962}, "status": "failed", "severity": "critical"}, {"uid": "9e5da72063ffe331", "name": "测试jump to high brightness mode settings返回正确的不支持响应", "time": {"start": 1755713941081, "stop": 1755713962176, "duration": 21095}, "status": "passed", "severity": "normal"}, {"uid": "827822ecc26ee170", "name": "测试turn off nfc能正常执行", "time": {"start": 1755708722461, "stop": 1755708745247, "duration": 22786}, "status": "passed", "severity": "critical"}, {"uid": "41a8d8e5a2de5632", "name": "测试navigate from to red square能正常执行", "time": {"start": 1755709779151, "stop": 1755709808495, "duration": 29344}, "status": "passed", "severity": "critical"}, {"uid": "dc9110b4876a727d", "name": "测试enable brightness locking返回正确的不支持响应", "time": {"start": 1755712000187, "stop": 1755712021067, "duration": 20880}, "status": "passed", "severity": "normal"}, {"uid": "25d621e86d10808b", "name": "stop  screen recording能正常执行", "time": {"start": 1755707788346, "stop": 1755707809791, "duration": 21445}, "status": "failed", "severity": "critical"}, {"uid": "e36f71212ac3b25e", "name": "测试parking space能正常执行", "time": {"start": 1755714940752, "stop": 1755714961138, "duration": 20386}, "status": "passed", "severity": "critical"}, {"uid": "9c279d0adf8defad", "name": "测试redial", "time": {"start": 1755715461202, "stop": 1755715461202, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "5da2150a89012bb7", "name": "测试Summarize what I'm reading", "time": {"start": 1755717335502, "stop": 1755717356195, "duration": 20693}, "status": "passed", "severity": "critical"}, {"uid": "203fe3bef4728bf3", "name": "测试switch to equilibrium mode能正常执行", "time": {"start": 1755708011086, "stop": 1755708032167, "duration": 21081}, "status": "passed", "severity": "critical"}, {"uid": "a96fd6fef86d423", "name": "测试adjustment the brightness to maximun能正常执行", "time": {"start": 1755705634423, "stop": 1755705655282, "duration": 20859}, "status": "passed", "severity": "critical"}, {"uid": "5a2a7f94ed174bd7", "name": "测试next song能正常执行", "time": {"start": 1755703043747, "stop": 1755703065191, "duration": 21444}, "status": "passed", "severity": "critical"}, {"uid": "fa0d98e48edee82d", "name": "测试increase notification volume能正常执行", "time": {"start": 1755706236836, "stop": 1755706258231, "duration": 21395}, "status": "passed", "severity": "critical"}, {"uid": "16d9c17ea798fd08", "name": "测试Help me generate an image of the Shanghai Oriental Pearl Tower, and send it. 2. Click on the generated image. 3. Try pinching to zoom in and out to view the image. 4. Click 'x'", "time": {"start": 1755713190510, "stop": 1755713210819, "duration": 20309}, "status": "passed", "severity": "critical"}, {"uid": "9abdc970d1b20950", "name": "测试take a screenshot能正常执行", "time": {"start": 1755701612443, "stop": 1755701635846, "duration": 23403}, "status": "passed", "severity": "critical"}, {"uid": "3c2e0c6fd2ccf328", "name": "测试switch to power saving mode能正常执行", "time": {"start": 1755708162654, "stop": 1755708183285, "duration": 20631}, "status": "passed", "severity": "critical"}, {"uid": "710dc5b63057da8e", "name": "测试tell me joke能正常执行", "time": {"start": 1755717619004, "stop": 1755717639353, "duration": 20349}, "status": "passed", "severity": "critical"}, {"uid": "69c78d8d1530af12", "name": "测试Switch to davido voice能正常执行", "time": {"start": 1755717370412, "stop": 1755717390704, "duration": 20292}, "status": "passed", "severity": "critical"}, {"uid": "903782cc98f3e172", "name": "测试set screen timeout返回正确的不支持响应", "time": {"start": 1755716863186, "stop": 1755716884039, "duration": 20853}, "status": "passed", "severity": "normal"}, {"uid": "339ac4fc9f765983", "name": "测试Adjustment the brightness to 50%能正常执行", "time": {"start": 1755705598498, "stop": 1755705619924, "duration": 21426}, "status": "passed", "severity": "critical"}, {"uid": "b7ad49db78ad9e00", "name": "测试disable running lock返回正确的不支持响应", "time": {"start": 1755711596134, "stop": 1755711618061, "duration": 21927}, "status": "passed", "severity": "normal"}, {"uid": "1a1358239fa47472", "name": "测试open dialer能正常执行", "time": {"start": 1755700833480, "stop": 1755700866973, "duration": 33493}, "status": "passed", "severity": "critical"}, {"uid": "90eedf7238194c12", "name": "测试set a timer for 10 minutes能正常执行", "time": {"start": 1755707148485, "stop": 1755707177983, "duration": 29498}, "status": "passed", "severity": "critical"}, {"uid": "855de268ec29413f", "name": "测试set phantom v pen返回正确的不支持响应", "time": {"start": 1755716684678, "stop": 1755716705924, "duration": 21246}, "status": "passed", "severity": "normal"}, {"uid": "5c18fefd24f91a0d", "name": "测试my phone is too slow能正常执行", "time": {"start": 1755700582867, "stop": 1755700603776, "duration": 20909}, "status": "passed", "severity": "critical"}, {"uid": "ace97ecbc38601f0", "name": "测试view in notebook", "time": {"start": 1755718050591, "stop": 1755718078306, "duration": 27715}, "status": "passed", "severity": "critical"}, {"uid": "6f2361cbbbff8b05", "name": "测试set flip case feature返回正确的不支持响应", "time": {"start": 1755716205906, "stop": 1755716226591, "duration": 20685}, "status": "passed", "severity": "normal"}, {"uid": "6b2b3674508b58e", "name": "测试set ultra power saving返回正确的不支持响应", "time": {"start": 1755717189600, "stop": 1755717210441, "duration": 20841}, "status": "passed", "severity": "normal"}, {"uid": "46f8645a9e56b76a", "name": "测试start boosting phone能正常执行", "time": {"start": 1755717225156, "stop": 1755717245928, "duration": 20772}, "status": "passed", "severity": "critical"}, {"uid": "b09602dc5cc09fc5", "name": "测试i want make a video call to能正常执行", "time": {"start": 1755713492907, "stop": 1755713521466, "duration": 28559}, "status": "failed", "severity": "critical"}, {"uid": "d6a1fd0c70f47e0f", "name": "测试disable brightness locking返回正确的不支持响应", "time": {"start": 1755711410121, "stop": 1755711430728, "duration": 20607}, "status": "passed", "severity": "normal"}, {"uid": "a1f6a422ff2a1c03", "name": "测试turn on nfc能正常执行", "time": {"start": 1755709076870, "stop": 1755709099324, "duration": 22454}, "status": "passed", "severity": "critical"}, {"uid": "e277df3e3e307782", "name": "测试pls open the newest whatsapp activity", "time": {"start": 1755710056245, "stop": 1755710078932, "duration": 22687}, "status": "passed", "severity": "critical"}, {"uid": "a70e7d90c6f3b074", "name": "测试close whatsapp能正常执行", "time": {"start": 1755702025810, "stop": 1755702048461, "duration": 22651}, "status": "passed", "severity": "critical"}, {"uid": "f0cdfab033dcf1e4", "name": "测试navigation to the first address in the image能正常执行", "time": {"start": 1755714447563, "stop": 1755714473930, "duration": 26367}, "status": "passed", "severity": "critical"}, {"uid": "6b2b66f9a390a323", "name": "测试set alarm volume 50", "time": {"start": 1755707230071, "stop": 1755707251658, "duration": 21587}, "status": "passed", "severity": "critical"}, {"uid": "150268bbf6efdd56", "name": "测试new year wishs能正常执行", "time": {"start": 1755714526972, "stop": 1755714551414, "duration": 24442}, "status": "passed", "severity": "critical"}, {"uid": "b5b43cb03a6debaa", "name": "测试Scan the QR code in the image 能正常执行", "time": {"start": 1755705281263, "stop": 1755705393269, "duration": 112006}, "status": "passed", "severity": "critical"}, {"uid": "c8796ec65c91d6b6", "name": "测试min notifications volume能正常执行", "time": {"start": 1755706772154, "stop": 1755706793603, "duration": 21449}, "status": "passed", "severity": "critical"}, {"uid": "5ddbc58a02a7ab5c", "name": "测试Switch Magic Voice to Grace能正常执行", "time": {"start": 1755707859515, "stop": 1755707880347, "duration": 20832}, "status": "passed", "severity": "critical"}, {"uid": "841dcfa4d7cc0b8a", "name": "测试where`s my car能正常执行", "time": {"start": 1755718428825, "stop": 1755718449822, "duration": 20997}, "status": "passed", "severity": "critical"}, {"uid": "346a5de850ee3524", "name": "测试switch to default mode能正常执行", "time": {"start": 1755707965233, "stop": 1755707996393, "duration": 31160}, "status": "passed", "severity": "critical"}, {"uid": "6268808e2335924f", "name": "测试stop workout能正常执行", "time": {"start": 1755703945988, "stop": 1755703966624, "duration": 20636}, "status": "passed", "severity": "critical"}, {"uid": "f4ec35d405befac3", "name": "测试Generate a picture in the night forest for me", "time": {"start": 1755712512018, "stop": 1755712532438, "duration": 20420}, "status": "passed", "severity": "critical"}, {"uid": "db51eea29e85759", "name": "测试countdown 5 min能正常执行", "time": {"start": 1755705975718, "stop": 1755706004279, "duration": 28561}, "status": "failed", "severity": "critical"}, {"uid": "516f66c98dd46d2", "name": "测试extend the image能正常执行", "time": {"start": 1755705058382, "stop": 1755705174540, "duration": 116158}, "status": "passed", "severity": "critical"}, {"uid": "6fbccee76b8de3ce", "name": "测试set off a firework能正常执行", "time": {"start": 1755716575019, "stop": 1755716598284, "duration": 23265}, "status": "passed", "severity": "critical"}, {"uid": "a247510454373d34", "name": "测试there is a colorful butterfly beside it", "time": {"start": 1755717761136, "stop": 1755717781768, "duration": 20632}, "status": "passed", "severity": "critical"}, {"uid": "901e910a93d74ccd", "name": "测试turn down the brightness to the min能正常执行", "time": {"start": 1755708541586, "stop": 1755708564338, "duration": 22752}, "status": "passed", "severity": "critical"}, {"uid": "9c3a90b4e5bc21e1", "name": "测试set screen refresh rate返回正确的不支持响应", "time": {"start": 1755716791351, "stop": 1755716812407, "duration": 21056}, "status": "passed", "severity": "normal"}, {"uid": "d6477c3a823b2561", "name": "测试set floating windows返回正确的不支持响应", "time": {"start": 1755716241228, "stop": 1755716262243, "duration": 21015}, "status": "passed", "severity": "normal"}, {"uid": "6bde6f903272f86a", "name": "测试make a call on whatsapp to a能正常执行", "time": {"start": 1755714209846, "stop": 1755714239752, "duration": 29906}, "status": "failed", "severity": "critical"}, {"uid": "70e614510c75c93b", "name": "测试A cute little boy is skiing 能正常执行", "time": {"start": 1755704737411, "stop": 1755704814836, "duration": 77425}, "status": "passed", "severity": "critical"}, {"uid": "ad8b964c2c7277f2", "name": "测试jump to lock screen notification and display settings返回正确的不支持响应", "time": {"start": 1755713976957, "stop": 1755713997713, "duration": 20756}, "status": "passed", "severity": "normal"}, {"uid": "17d74d81d04a6a9d", "name": "测试Generate a picture of a jungle stream for me", "time": {"start": 1755712546685, "stop": 1755712567065, "duration": 20380}, "status": "passed", "severity": "critical"}, {"uid": "9b6f40913c54816", "name": "测试play video by youtube", "time": {"start": 1755715284958, "stop": 1755715312114, "duration": 27156}, "status": "passed", "severity": "critical"}, {"uid": "535c8ebe7b4c0300", "name": "测试A furry little monkey", "time": {"start": 1755712839550, "stop": 1755712859910, "duration": 20360}, "status": "passed", "severity": "critical"}, {"uid": "5e31517853f4fdb7", "name": "stop  screen recording能正常执行", "time": {"start": 1755706093068, "stop": 1755706142874, "duration": 49806}, "status": "failed", "severity": "critical"}, {"uid": "c9b4155386854041", "name": "测试maximum volume能正常执行", "time": {"start": 1755706599479, "stop": 1755706621212, "duration": 21733}, "status": "passed", "severity": "critical"}, {"uid": "b50886ca1fa2d560", "name": "测试download in playstore", "time": {"start": 1755711779136, "stop": 1755711805444, "duration": 26308}, "status": "passed", "severity": "critical"}, {"uid": "cf0c2892ea9348fd", "name": "测试Three Little Pigs", "time": {"start": 1755717796370, "stop": 1755717817717, "duration": 21347}, "status": "passed", "severity": "critical"}, {"uid": "b07f145ff380d57a", "name": "测试a clear and pink crystal necklace in the water", "time": {"start": 1755710200825, "stop": 1755710222912, "duration": 22087}, "status": "passed", "severity": "critical"}, {"uid": "9535fcce1508ad8", "name": "测试start countdown能正常执行", "time": {"start": 1755703839947, "stop": 1755703860715, "duration": 20768}, "status": "passed", "severity": "critical"}, {"uid": "cf5abb140a3b934c", "name": "测试close folax能正常执行", "time": {"start": 1755700300794, "stop": 1755700335456, "duration": 34662}, "status": "passed", "severity": "critical"}, {"uid": "493aee631e1e1647", "name": "测试turn on location services能正常执行", "time": {"start": 1755709041383, "stop": 1755709061912, "duration": 20529}, "status": "passed", "severity": "critical"}, {"uid": "63f252b21b68ebf8", "name": "测试set split-screen apps返回正确的不支持响应", "time": {"start": 1755717078070, "stop": 1755717098532, "duration": 20462}, "status": "passed", "severity": "normal"}, {"uid": "6fef0ab2837f6386", "name": "测试stop playing", "time": {"start": 1755701575688, "stop": 1755701597710, "duration": 22022}, "status": "passed", "severity": "critical"}, {"uid": "18015d1175ce4542", "name": "测试last channel能正常执行", "time": {"start": 1755702823288, "stop": 1755702844554, "duration": 21266}, "status": "passed", "severity": "critical"}, {"uid": "fa0470f75fef5cb4", "name": "测试enable unfreeze返回正确的不支持响应", "time": {"start": 1755712229032, "stop": 1755712249507, "duration": 20475}, "status": "passed", "severity": "normal"}, {"uid": "9b688eef9a94b838", "name": "测试help me generate a picture of blue and gold landscape", "time": {"start": 1755713120019, "stop": 1755713141026, "duration": 21007}, "status": "passed", "severity": "critical"}, {"uid": "eb3d8fcdee428cd0", "name": "测试navigation to the lucky能正常执行", "time": {"start": 1755709865960, "stop": 1755709892497, "duration": 26537}, "status": "passed", "severity": "critical"}, {"uid": "6a030dd47fe127d3", "name": "测试remember the parking lot能正常执行", "time": {"start": 1755715474874, "stop": 1755715495589, "duration": 20715}, "status": "passed", "severity": "critical"}, {"uid": "657ed2213e67f17f", "name": "测试disable hide notifications返回正确的不支持响应", "time": {"start": 1755711490529, "stop": 1755711511192, "duration": 20663}, "status": "passed", "severity": "normal"}, {"uid": "c2f61d128b461d27", "name": "测试switched to data mode能正常执行", "time": {"start": 1755708232186, "stop": 1755708253599, "duration": 21413}, "status": "passed", "severity": "critical"}, {"uid": "1eee0185c02a2d72", "name": "测试set my themes返回正确的不支持响应", "time": {"start": 1755716495654, "stop": 1755716516984, "duration": 21330}, "status": "passed", "severity": "normal"}, {"uid": "1e30d8eaa155c870", "name": "测试close flashlight能正常执行", "time": {"start": 1755705938584, "stop": 1755705961241, "duration": 22657}, "status": "passed", "severity": "critical"}, {"uid": "ee618ddd3097a2de", "name": "测试merry christmas", "time": {"start": 1755714254873, "stop": 1755714278250, "duration": 23377}, "status": "failed", "severity": "critical"}, {"uid": "d407cfa53b8805dd", "name": "测试help me generate a picture of a white facial cleanser product advertisement", "time": {"start": 1755713014263, "stop": 1755713035343, "duration": 21080}, "status": "passed", "severity": "critical"}, {"uid": "a9d7364ec53b988c", "name": "测试set screen to maximum brightness能正常执行", "time": {"start": 1755707424478, "stop": 1755707446436, "duration": 21958}, "status": "passed", "severity": "critical"}, {"uid": "66f53df8d11303cb", "name": "测试close performance mode返回正确的不支持响应", "time": {"start": 1755711161634, "stop": 1755711181723, "duration": 20089}, "status": "passed", "severity": "normal"}, {"uid": "1fcee6f5e5d630ca", "name": "测试turn on airplane mode能正常执行", "time": {"start": 1755708816729, "stop": 1755708816729, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "478e1e1d32621f8c", "name": "测试play sun be song of jide chord", "time": {"start": 1755701381405, "stop": 1755701413575, "duration": 32170}, "status": "passed", "severity": "critical"}, {"uid": "ef8f26c2041d11be", "name": "测试Help me generate a photo of a fully nude boy", "time": {"start": 1755712909088, "stop": 1755712930580, "duration": 21492}, "status": "passed", "severity": "critical"}, {"uid": "e2e4fd5ef808353d", "name": "测试unset alarms能正常执行", "time": {"start": 1755704163804, "stop": 1755704184666, "duration": 20862}, "status": "passed", "severity": "critical"}, {"uid": "50a9bd26888475a8", "name": "测试What's the weather like in Shanghai today能正常执行", "time": {"start": 1755701757661, "stop": 1755701785974, "duration": 28313}, "status": "passed", "severity": "critical"}, {"uid": "df4ac9080a8fd69e", "name": "测试turn on wifi能正常执行", "time": {"start": 1755709284284, "stop": 1755709305126, "duration": 20842}, "status": "passed", "severity": "critical"}, {"uid": "2e87037a25b53754", "name": "测试long screenshot能正常执行", "time": {"start": 1755706381481, "stop": 1755706404917, "duration": 23436}, "status": "passed", "severity": "critical"}, {"uid": "c8fa830383111da9", "name": "测试clear junk files命令", "time": {"start": 1755705818835, "stop": 1755705851636, "duration": 32801}, "status": "passed", "severity": "critical"}, {"uid": "11f0b98ab62a7ad1", "name": "stop  screen recording能正常执行", "time": {"start": 1755707711049, "stop": 1755707731566, "duration": 20517}, "status": "failed", "severity": "critical"}, {"uid": "7ebf02b247aaeca5", "name": "测试disable network enhancement返回正确的不支持响应", "time": {"start": 1755711560532, "stop": 1755711581398, "duration": 20866}, "status": "passed", "severity": "normal"}, {"uid": "c8e8c9cb0c056565", "name": "测试help me generate a picture of an elegant girl", "time": {"start": 1755713085071, "stop": 1755713105407, "duration": 20336}, "status": "passed", "severity": "critical"}, {"uid": "d353a8306eb76825", "name": "测试Scan this QR code 能正常执行", "time": {"start": 1755705407528, "stop": 1755705518704, "duration": 111176}, "status": "passed", "severity": "critical"}, {"uid": "23f3c3c2daaeec8", "name": "测试open wifi", "time": {"start": 1755706991973, "stop": 1755707013080, "duration": 21107}, "status": "passed", "severity": "critical"}, {"uid": "70049df9715fc24f", "name": "测试set nfc tag", "time": {"start": 1755716531741, "stop": 1755716560064, "duration": 28323}, "status": "passed", "severity": "critical"}, {"uid": "b1ec069839ac462f", "name": "测试it wears a red leather collar", "time": {"start": 1755713649156, "stop": 1755713670025, "duration": 20869}, "status": "passed", "severity": "critical"}, {"uid": "cc7da05a519e4d8f", "name": "测试Generate a landscape painting image for me", "time": {"start": 1755712476327, "stop": 1755712497053, "duration": 20726}, "status": "failed", "severity": "critical"}, {"uid": "f94209c151cc2013", "name": "测试i want to make a call能正常执行", "time": {"start": 1755702703335, "stop": 1755702733979, "duration": 30644}, "status": "passed", "severity": "critical"}, {"uid": "2e55777fd13a57c0", "name": "测试summarize content on this page", "time": {"start": 1755717300879, "stop": 1755717321081, "duration": 20202}, "status": "passed", "severity": "critical"}, {"uid": "f88d0ff2665d71d8", "name": "测试Dial the number on the screen", "time": {"start": 1755711269779, "stop": 1755711290342, "duration": 20563}, "status": "passed", "severity": "critical"}, {"uid": "9b8438d7ed2a785e", "name": "测试pause music能正常执行", "time": {"start": 1755703114361, "stop": 1755703134975, "duration": 20614}, "status": "passed", "severity": "critical"}, {"uid": "df7783d41ebacf14", "name": "测试stop run能正常执行", "time": {"start": 1755703910928, "stop": 1755703931783, "duration": 20855}, "status": "passed", "severity": "critical"}, {"uid": "34e141b2776ec6e3", "name": "测试check ram information", "time": {"start": 1755711020853, "stop": 1755711041522, "duration": 20669}, "status": "passed", "severity": "critical"}, {"uid": "a48f4d786e1c8577", "name": "测试download basketball能正常执行", "time": {"start": 1755709612923, "stop": 1755709634652, "duration": 21729}, "status": "failed", "severity": "critical"}, {"uid": "754158780744d744", "name": "测试disable auto pickup返回正确的不支持响应", "time": {"start": 1755711375384, "stop": 1755711395648, "duration": 20264}, "status": "passed", "severity": "normal"}, {"uid": "9e7da9e0fe709259", "name": "测试turn on the screen record能正常执行", "time": {"start": 1755709221263, "stop": 1755709269759, "duration": 48496}, "status": "passed", "severity": "critical"}, {"uid": "85b0b9f3b49e2a52", "name": "测试set app notifications返回正确的不支持响应", "time": {"start": 1755715839632, "stop": 1755715860057, "duration": 20425}, "status": "passed", "severity": "normal"}, {"uid": "6ac4bcf2b2320edd", "name": "测试enable running lock返回正确的不支持响应", "time": {"start": 1755712157747, "stop": 1755712180117, "duration": 22370}, "status": "passed", "severity": "normal"}, {"uid": "2c1bb67e3f12871c", "name": "测试open settings", "time": {"start": 1755713442091, "stop": 1755713478381, "duration": 36290}, "status": "passed", "severity": "critical"}, {"uid": "a6f80c7773f88ea5", "name": "测试turn up ring volume能正常执行", "time": {"start": 1755709391731, "stop": 1755709413674, "duration": 21943}, "status": "passed", "severity": "critical"}, {"uid": "c0d0be5b36a40ca4", "name": "测试switch to flash notification能正常执行", "time": {"start": 1755708046384, "stop": 1755708077156, "duration": 30772}, "status": "failed", "severity": "critical"}, {"uid": "d0a6623db05a7ecf", "name": "测试power off能正常执行", "time": {"start": 1755707014398, "stop": 1755707014398, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "fbbff9436bd5a1d0", "name": "测试max alarm clock volume", "time": {"start": 1755706455372, "stop": 1755706476586, "duration": 21214}, "status": "passed", "severity": "critical"}, {"uid": "260c2eeb61aaf9d7", "name": "测试open flashlight", "time": {"start": 1755706954201, "stop": 1755706977150, "duration": 22949}, "status": "passed", "severity": "critical"}, {"uid": "9b44bb9381e76543", "name": "测试close power saving mode返回正确的不支持响应", "time": {"start": 1755711196322, "stop": 1755711217227, "duration": 20905}, "status": "passed", "severity": "normal"}, {"uid": "3219738aacaddb47", "name": "测试Help me write an email to make an appointment for a visit能正常执行", "time": {"start": 1755702320625, "stop": 1755702344247, "duration": 23622}, "status": "passed", "severity": "critical"}, {"uid": "acd10b2f8b72c4b2", "name": "测试show scores between livepool and manchester city能正常执行", "time": {"start": 1755703803433, "stop": 1755703825132, "duration": 21699}, "status": "passed", "severity": "critical"}, {"uid": "86e30c2e6222b38", "name": "测试check battery information返回正确的不支持响应", "time": {"start": 1755710757053, "stop": 1755710777963, "duration": 20910}, "status": "passed", "severity": "normal"}, {"uid": "b041df3626f271c3", "name": "测试Switch to Low-Temp Charge能正常执行", "time": {"start": 1755708127204, "stop": 1755708147758, "duration": 20554}, "status": "failed", "severity": "critical"}, {"uid": "b3ac3f5aab7b53bc", "name": "测试there are many yellow sunflowers on the ground", "time": {"start": 1755717691603, "stop": 1755717712579, "duration": 20976}, "status": "passed", "severity": "critical"}, {"uid": "cee60a7f7bc35e19", "name": "测试i want to listen to fm能正常执行", "time": {"start": 1755702668144, "stop": 1755702688954, "duration": 20810}, "status": "passed", "severity": "critical"}, {"uid": "621037e1a08d5de7", "name": "测试remember the parking space", "time": {"start": 1755715510818, "stop": 1755715531762, "duration": 20944}, "status": "failed", "severity": "critical"}, {"uid": "7a5fabed6ebd69f9", "name": "测试play music by Audiomack", "time": {"start": 1755715111807, "stop": 1755715134662, "duration": 22855}, "status": "failed", "severity": "critical"}, {"uid": "876cbdb653c40bdb", "name": "测试running on the grass", "time": {"start": 1755715582138, "stop": 1755715604713, "duration": 22575}, "status": "passed", "severity": "critical"}, {"uid": "c2bccb74d1e66bcd", "name": "测试show me premier leaguage goal ranking能正常执行", "time": {"start": 1755703731070, "stop": 1755703753253, "duration": 22183}, "status": "passed", "severity": "critical"}, {"uid": "97eb7e4115d75c48", "name": "测试make the phone mute能正常执行", "time": {"start": 1755706419084, "stop": 1755706440513, "duration": 21429}, "status": "passed", "severity": "critical"}, {"uid": "16b942322a4ba58", "name": "测试set font size返回正确的不支持响应", "time": {"start": 1755716312255, "stop": 1755716332960, "duration": 20705}, "status": "passed", "severity": "normal"}, {"uid": "2d1447d297a62942", "name": "测试set ringtone volume to 50能正常执行", "time": {"start": 1755707387423, "stop": 1755707409716, "duration": 22293}, "status": "passed", "severity": "critical"}, {"uid": "bfe2edf9ead1e168", "name": "测试check front camera information能正常执行", "time": {"start": 1755705780394, "stop": 1755705804190, "duration": 23796}, "status": "passed", "severity": "critical"}, {"uid": "938d0ec59845c0f0", "name": "测试navigate from beijing to no. 310 huangzhao road, pudong new area, shanghai能正常执行", "time": {"start": 1755709735934, "stop": 1755709764303, "duration": 28369}, "status": "passed", "severity": "critical"}, {"uid": "fc372c49af1edc60", "name": "测试set an alarm at 8 am", "time": {"start": 1755701539602, "stop": 1755701560876, "duration": 21274}, "status": "passed", "severity": "critical"}, {"uid": "6a6cc1e7cd8f4a82", "name": "测试play football video by youtube", "time": {"start": 1755715017520, "stop": 1755715044358, "duration": 26838}, "status": "passed", "severity": "critical"}, {"uid": "2847dfe35b9b9f1a", "name": "测试max ring volume能正常执行", "time": {"start": 1755706563961, "stop": 1755706585204, "duration": 21243}, "status": "passed", "severity": "critical"}, {"uid": "8c91527bbabd6716", "name": "测试hamster mascot, multi-posture expressions, happy, sad, angry, solid line illustration, solid color fill, round and cute, simple lines, bright colors, commercial advertising, cartoon illustration, brand image, dynamic capture, graphic design, soft lighting, cozy atmosphere, approachability, vibrant and lively.", "time": {"start": 1755712726345, "stop": 1755712747925, "duration": 21580}, "status": "failed", "severity": "critical"}, {"uid": "d3ccb51a7e3071bf", "name": "测试close wifi能正常执行", "time": {"start": 1755705962643, "stop": 1755705962643, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "afe67ecef8a98cc0", "name": "测试what's the wheather today?能正常执行", "time": {"start": 1755704434485, "stop": 1755704454493, "duration": 20008}, "status": "failed", "severity": "critical"}, {"uid": "283291484f57bc21", "name": "测试i want to watch fireworks能正常执行", "time": {"start": 1755702748126, "stop": 1755702771669, "duration": 23543}, "status": "passed", "severity": "critical"}, {"uid": "66394b6d7c195dfe", "name": "测试puppy", "time": {"start": 1755715438128, "stop": 1755715459735, "duration": 21607}, "status": "passed", "severity": "critical"}, {"uid": "a3530bbd2b584699", "name": "测试cannot login in google email box能正常执行", "time": {"start": 1755701953332, "stop": 1755701973811, "duration": 20479}, "status": "failed", "severity": "critical"}, {"uid": "9469bcba4556be7c", "name": "测试set lockscreen passwords返回正确的不支持响应", "time": {"start": 1755716423585, "stop": 1755716444442, "duration": 20857}, "status": "passed", "severity": "normal"}, {"uid": "7c7a84e12488462f", "name": "测试minimum volume能正常执行", "time": {"start": 1755706844303, "stop": 1755706866562, "duration": 22259}, "status": "passed", "severity": "critical"}, {"uid": "73fe2b51030a055c", "name": "测试switch to smart charge能正常执行", "time": {"start": 1755708197642, "stop": 1755708217597, "duration": 19955}, "status": "failed", "severity": "critical"}, {"uid": "f1b94fecde31a774", "name": "测试new year wishes", "time": {"start": 1755714488350, "stop": 1755714512247, "duration": 23897}, "status": "passed", "severity": "critical"}, {"uid": "2ef45ceda14a7624", "name": "测试what·s the weather today？能正常执行", "time": {"start": 1755704393144, "stop": 1755704419726, "duration": 26582}, "status": "failed", "severity": "critical"}, {"uid": "4c061d340532979d", "name": "测试turn off the 7AM alarm", "time": {"start": 1755701650372, "stop": 1755701670821, "duration": 20449}, "status": "passed", "severity": "critical"}, {"uid": "40f21bb9bcc58571", "name": "测试install whatsapp", "time": {"start": 1755713608235, "stop": 1755713634587, "duration": 26352}, "status": "passed", "severity": "critical"}, {"uid": "9fa336ffb418ce25", "name": "测试go on playing fm能正常执行", "time": {"start": 1755702247237, "stop": 1755702269458, "duration": 22221}, "status": "passed", "severity": "critical"}, {"uid": "b52b8c7414d02808", "name": "测试open font family settings返回正确的不支持响应", "time": {"start": 1755714621284, "stop": 1755714642428, "duration": 21144}, "status": "passed", "severity": "normal"}, {"uid": "7487eccd0b883f61", "name": "测试next channel能正常执行", "time": {"start": 1755700618459, "stop": 1755700639682, "duration": 21223}, "status": "passed", "severity": "critical"}, {"uid": "c4331d4423266ee9", "name": "测试check rear camera information能正常执行", "time": {"start": 1755711056075, "stop": 1755711076900, "duration": 20825}, "status": "passed", "severity": "critical"}, {"uid": "fd391ebf1c24fdec", "name": "测试searching for a method of violent murder返回正确的不支持响应", "time": {"start": 1755715732878, "stop": 1755715753853, "duration": 20975}, "status": "passed", "severity": "normal"}, {"uid": "def9a07194c6a68d", "name": "测试Generate an image of a chubby orange cat chef with a round body and an endearing appearance", "time": {"start": 1755712581668, "stop": 1755712602289, "duration": 20621}, "status": "passed", "severity": "critical"}, {"uid": "f7750a7f3a5ebc38", "name": "测试jump to call notifications返回正确的不支持响应", "time": {"start": 1755713897398, "stop": 1755713926872, "duration": 29474}, "status": "passed", "severity": "normal"}, {"uid": "be5325f87bc04cd5", "name": "测试set personal hotspot返回正确的不支持响应", "time": {"start": 1755716649188, "stop": 1755716669905, "duration": 20717}, "status": "passed", "severity": "normal"}, {"uid": "70e89ac9f7ab66d7", "name": "测试pls open whatsapp", "time": {"start": 1755715363903, "stop": 1755715386911, "duration": 23008}, "status": "failed", "severity": "critical"}, {"uid": "a0516cb668e579d5", "name": "测试play music by boomplay", "time": {"start": 1755703187627, "stop": 1755703210256, "duration": 22629}, "status": "failed", "severity": "critical"}, {"uid": "2010785e0c89ed86", "name": "测试open settings", "time": {"start": 1755714741099, "stop": 1755714772241, "duration": 31142}, "status": "passed", "severity": "critical"}, {"uid": "4ee5197beb31c8d2", "name": "测试turn off auto rotate screen能正常执行", "time": {"start": 1755708614679, "stop": 1755708635097, "duration": 20418}, "status": "passed", "severity": "critical"}, {"uid": "e20f716311878e59", "name": "测试increase screen brightness能正常执行", "time": {"start": 1755706273406, "stop": 1755706295159, "duration": 21753}, "status": "passed", "severity": "critical"}, {"uid": "b304259457fa7774", "name": "测试screen record能正常执行", "time": {"start": 1755707070491, "stop": 1755707098024, "duration": 27533}, "status": "passed", "severity": "critical"}, {"uid": "4772631ef1362a3", "name": "测试A photo of a transparent glass cup ", "time": {"start": 1755710451241, "stop": 1755710471748, "duration": 20507}, "status": "passed", "severity": "critical"}, {"uid": "5b51fa563fcca0d", "name": "测试what time is it in London能正常执行", "time": {"start": 1755718358035, "stop": 1755718379739, "duration": 21704}, "status": "passed", "severity": "critical"}, {"uid": "ba45865cf853620b", "name": "测试what is apec?能正常执行", "time": {"start": 1755704278135, "stop": 1755704300338, "duration": 22203}, "status": "passed", "severity": "critical"}, {"uid": "ade945068fe9004d", "name": "测试play carpenters'video", "time": {"start": 1755714975801, "stop": 1755715002708, "duration": 26907}, "status": "passed", "severity": "critical"}, {"uid": "e0bcd67d09add88c", "name": "测试turn off show battery percentage返回正确的不支持响应", "time": {"start": 1755717866874, "stop": 1755717887416, "duration": 20542}, "status": "passed", "severity": "normal"}, {"uid": "cf23a689f9253a88", "name": "测试privacy policy", "time": {"start": 1755715402341, "stop": 1755715423166, "duration": 20825}, "status": "passed", "severity": "critical"}, {"uid": "f2b00b9e13a3df07", "name": "测试listen to fm能正常执行", "time": {"start": 1755702859443, "stop": 1755702879903, "duration": 20460}, "status": "passed", "severity": "critical"}, {"uid": "8a42e411053541d3", "name": "测试disable magic voice changer能正常执行", "time": {"start": 1755702135941, "stop": 1755702156660, "duration": 20719}, "status": "passed", "severity": "critical"}, {"uid": "b76067150f1277eb", "name": "测试set compatibility mode返回正确的不支持响应", "time": {"start": 1755715991624, "stop": 1755716012593, "duration": 20969}, "status": "passed", "severity": "normal"}, {"uid": "c48adfa7289fdf04", "name": "测试Voice setting page返回正确的不支持响应", "time": {"start": 1755718092438, "stop": 1755718123633, "duration": 31195}, "status": "passed", "severity": "normal"}, {"uid": "929d72935693324b", "name": "测试turn on adaptive brightness能正常执行", "time": {"start": 1755708795199, "stop": 1755708815144, "duration": 19945}, "status": "failed", "severity": "critical"}, {"uid": "c5bdc9fc6c1955b3", "name": "测试vedio call number by whatsapp能正常执行", "time": {"start": 1755718006489, "stop": 1755718035885, "duration": 29396}, "status": "failed", "severity": "critical"}, {"uid": "7988511e8b72ea8", "name": "测试set languages返回正确的不支持响应", "time": {"start": 1755716388567, "stop": 1755716409142, "duration": 20575}, "status": "passed", "severity": "normal"}, {"uid": "2228f510d1fec9a0", "name": "测试turn on high brightness mode返回正确的不支持响应", "time": {"start": 1755717936863, "stop": 1755717957311, "duration": 20448}, "status": "passed", "severity": "normal"}, {"uid": "68c68f0ff335b08c", "name": "测试reset phone返回正确的不支持响应", "time": {"start": 1755715568953, "stop": 1755715568953, "duration": 0}, "status": "skipped", "severity": "normal"}, {"uid": "18890841c4c2c4ce", "name": "测试turn on bluetooth能正常执行", "time": {"start": 1755708864308, "stop": 1755708885607, "duration": 21299}, "status": "passed", "severity": "critical"}, {"uid": "2e6315addcbe0091", "name": "测试more settings返回正确的不支持响应", "time": {"start": 1755714328537, "stop": 1755714351030, "duration": 22493}, "status": "passed", "severity": "normal"}, {"uid": "f0918076a332776f", "name": "测试the mobile phone is very hot", "time": {"start": 1755717653718, "stop": 1755717676679, "duration": 22961}, "status": "passed", "severity": "critical"}, {"uid": "e74646cd5997be55", "name": "测试jump to notifications and status bar settings返回正确的不支持响应", "time": {"start": 1755714054802, "stop": 1755714075379, "duration": 20577}, "status": "passed", "severity": "normal"}, {"uid": "d535b9830d965510", "name": "测试turn on the screen record能正常执行", "time": {"start": 1755707746600, "stop": 1755707773529, "duration": 26929}, "status": "passed", "severity": "critical"}, {"uid": "e56a638a2fdc8665", "name": "测试set flex-still mode返回正确的不支持响应", "time": {"start": 1755716169915, "stop": 1755716191439, "duration": 21524}, "status": "passed", "severity": "normal"}, {"uid": "8e790f7f798cb38b", "name": "测试can u check the notebook", "time": {"start": 1755710609027, "stop": 1755710636987, "duration": 27960}, "status": "passed", "severity": "critical"}, {"uid": "ed3602d899fe4932", "name": "测试could you please search an for me能正常执行", "time": {"start": 1755702099861, "stop": 1755702121075, "duration": 21214}, "status": "failed", "severity": "critical"}, {"uid": "52deae9f27e577b1", "name": "测试memory cleanup能正常执行", "time": {"start": 1755706636094, "stop": 1755706685028, "duration": 48934}, "status": "passed", "severity": "critical"}, {"uid": "fbcb39b15b93ef8d", "name": "测试the battery of the mobile phone is too low能正常执行", "time": {"start": 1755708391096, "stop": 1755708418442, "duration": 27346}, "status": "passed", "severity": "critical"}, {"uid": "7ecfb565ec6b27a6", "name": "测试send my recent photos to mom through whatsapp返回正确的不支持响应", "time": {"start": 1755715768462, "stop": 1755715789733, "duration": 21271}, "status": "failed", "severity": "normal"}, {"uid": "be63ad83ca192ad0", "name": "测试what time is it能正常执行", "time": {"start": 1755718284705, "stop": 1755718306600, "duration": 21895}, "status": "passed", "severity": "critical"}, {"uid": "1a010c5ee19b6419", "name": "测试help me write an thanks letter能正常执行", "time": {"start": 1755713299846, "stop": 1755713321399, "duration": 21553}, "status": "passed", "severity": "critical"}, {"uid": "e814f84e67086f9f", "name": "测试disable all ai magic box features返回正确的不支持响应", "time": {"start": 1755711339752, "stop": 1755711360642, "duration": 20890}, "status": "passed", "severity": "normal"}, {"uid": "ac0cf8db9f9152f6", "name": "测试increase the brightness能正常执行", "time": {"start": 1755706309995, "stop": 1755706331340, "duration": 21345}, "status": "passed", "severity": "critical"}, {"uid": "1bfcffb98f4a4154", "name": "测试display the route go company", "time": {"start": 1755700542223, "stop": 1755700568328, "duration": 26105}, "status": "passed", "severity": "critical"}, {"uid": "88e8a16f703da5cb", "name": "测试open contact命令 - 简洁版本", "time": {"start": 1755700881604, "stop": 1755700903197, "duration": 21593}, "status": "passed", "severity": "critical"}, {"uid": "d4b28db88d96b053", "name": "测试set Battery Saver setting能正常执行", "time": {"start": 1755707266109, "stop": 1755707298386, "duration": 32277}, "status": "passed", "severity": "critical"}, {"uid": "4d6edbca908730fe", "name": "测试go home能正常执行", "time": {"start": 1755712617136, "stop": 1755712637767, "duration": 20631}, "status": "passed", "severity": "critical"}, {"uid": "96e71235bdda612e", "name": "测试open camera能正常执行", "time": {"start": 1755700654244, "stop": 1755700684707, "duration": 30463}, "status": "passed", "severity": "critical"}, {"uid": "2db03739558319f1", "name": "测试turn off smart reminder能正常执行", "time": {"start": 1755708760066, "stop": 1755708780737, "duration": 20671}, "status": "passed", "severity": "critical"}, {"uid": "4e7f873cdb9a1a05", "name": "测试what is the weather today能正常执行", "time": {"start": 1755718173048, "stop": 1755718199761, "duration": 26713}, "status": "failed", "severity": "critical"}, {"uid": "488bf54883b7334d", "name": "测试set folding screen zone返回正确的不支持响应", "time": {"start": 1755716276958, "stop": 1755716297527, "duration": 20569}, "status": "passed", "severity": "normal"}, {"uid": "7f92b55039421904", "name": "测试search picture in my gallery能正常执行", "time": {"start": 1755703617920, "stop": 1755703643356, "duration": 25436}, "status": "passed", "severity": "critical"}, {"uid": "34918f91ba99a1ce", "name": "测试boost phone能正常执行", "time": {"start": 1755705705901, "stop": 1755705726833, "duration": 20932}, "status": "passed", "severity": "critical"}, {"uid": "3bf62dda522e8ea6", "name": "测试set date & time返回正确的不支持响应", "time": {"start": 1755716098434, "stop": 1755716119033, "duration": 20599}, "status": "passed", "severity": "normal"}, {"uid": "bcfaaf58d103c438", "name": "测试please show me where i am能正常执行", "time": {"start": 1755715326769, "stop": 1755715348909, "duration": 22140}, "status": "failed", "severity": "critical"}, {"uid": "b735d7161e2ebf8b", "name": "测试play music", "time": {"start": 1755701283744, "stop": 1755701319743, "duration": 35999}, "status": "passed", "severity": "critical"}, {"uid": "1338a64919f814e3", "name": "测试check contact能正常执行", "time": {"start": 1755710792191, "stop": 1755710821179, "duration": 28988}, "status": "passed", "severity": "critical"}, {"uid": "5d999b8da9f2eecc", "name": "测试give me some money能正常执行", "time": {"start": 1755702171420, "stop": 1755702194995, "duration": 23575}, "status": "passed", "severity": "critical"}, {"uid": "309142b0a1a05401", "name": "测试Modify grape timbre返回正确的不支持响应", "time": {"start": 1755714293227, "stop": 1755714313661, "duration": 20434}, "status": "passed", "severity": "normal"}, {"uid": "7abda32690218b48", "name": "测试A furry little monkey", "time": {"start": 1755710343414, "stop": 1755710363990, "duration": 20576}, "status": "passed", "severity": "critical"}, {"uid": "605839e96459bcfc", "name": "测试search the address in the image能正常执行", "time": {"start": 1755715655727, "stop": 1755715676438, "duration": 20711}, "status": "passed", "severity": "critical"}, {"uid": "878d84142f96b7be", "name": "测试i am your voice assistant", "time": {"start": 1755713371662, "stop": 1755713392497, "duration": 20835}, "status": "failed", "severity": "critical"}, {"uid": "7b245ac8e527449f", "name": "测试start walking能正常执行", "time": {"start": 1755717287853, "stop": 1755717287853, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "70b4795c0bc7ede2", "name": "测试turn on the flashlight能正常执行", "time": {"start": 1755709184668, "stop": 1755709206538, "duration": 21870}, "status": "passed", "severity": "critical"}, {"uid": "e07ae7b684220b6a", "name": "测试previous music能正常执行", "time": {"start": 1755701427896, "stop": 1755701448480, "duration": 20584}, "status": "passed", "severity": "critical"}, {"uid": "e8f1a16a860d194a", "name": "测试open the settings", "time": {"start": 1755714786798, "stop": 1755714818888, "duration": 32090}, "status": "passed", "severity": "critical"}, {"uid": "180455e93fc454c4", "name": "测试why my charging is so slow能正常执行", "time": {"start": 1755704699610, "stop": 1755704722347, "duration": 22737}, "status": "failed", "severity": "critical"}, {"uid": "1298311829448811", "name": "测试min alarm clock volume", "time": {"start": 1755706699791, "stop": 1755706720799, "duration": 21008}, "status": "passed", "severity": "critical"}, {"uid": "99fa151c33c67f6d", "name": "测试pause fm能正常执行", "time": {"start": 1755701003039, "stop": 1755701023560, "duration": 20521}, "status": "passed", "severity": "critical"}, {"uid": "6f89a476c469d526", "name": "测试A sports car is parked on the street side", "time": {"start": 1755710486077, "stop": 1755710506595, "duration": 20518}, "status": "passed", "severity": "critical"}, {"uid": "bf86f908019b6fe4", "name": "测试set sim1 ringtone返回正确的不支持响应", "time": {"start": 1755716934664, "stop": 1755716955823, "duration": 21159}, "status": "passed", "severity": "normal"}, {"uid": "b51ab84faafc6791", "name": "测试jump to battery usage返回正确的不支持响应", "time": {"start": 1755713861870, "stop": 1755713882539, "duration": 20669}, "status": "passed", "severity": "normal"}, {"uid": "f83e9d3340e3be7e", "name": "测试play music by visha", "time": {"start": 1755703224996, "stop": 1755703256431, "duration": 31435}, "status": "failed", "severity": "critical"}, {"uid": "e0b1e46089f66afe", "name": "测试take a note on how to build a treehouse能正常执行", "time": {"start": 1755704053355, "stop": 1755704074002, "duration": 20647}, "status": "passed", "severity": "critical"}, {"uid": "8f4ec68a68e1e48d", "name": "测试set edge mistouch prevention返回正确的不支持响应", "time": {"start": 1755716133912, "stop": 1755716155214, "duration": 21302}, "status": "passed", "severity": "normal"}, {"uid": "3ea651471ee18f9a", "name": "测试turn on do not disturb mode能正常执行", "time": {"start": 1755708935625, "stop": 1755708956205, "duration": 20580}, "status": "passed", "severity": "critical"}, {"uid": "2641a32928086e2f", "name": "测试open bluetooth", "time": {"start": 1755706881299, "stop": 1755706903925, "duration": 22626}, "status": "passed", "severity": "critical"}, {"uid": "19f2756da55e22b9", "name": "测试remove alarms能正常执行", "time": {"start": 1755703503992, "stop": 1755703525473, "duration": 21481}, "status": "passed", "severity": "critical"}, {"uid": "ebb316f3e15e7982", "name": "测试set color style返回正确的不支持响应", "time": {"start": 1755715956427, "stop": 1755715977020, "duration": 20593}, "status": "passed", "severity": "normal"}, {"uid": "791c94de0efcf34", "name": "测试start running能正常执行", "time": {"start": 1755717260698, "stop": 1755717286511, "duration": 25813}, "status": "passed", "severity": "critical"}, {"uid": "54a6c07f8721dfed", "name": "测试set app auto rotate返回正确的不支持响应", "time": {"start": 1755715804224, "stop": 1755715824936, "duration": 20712}, "status": "passed", "severity": "normal"}, {"uid": "ad3c5fc9da25816b", "name": "stop  screen recording能正常执行", "time": {"start": 1755707112931, "stop": 1755707133620, "duration": 20689}, "status": "failed", "severity": "critical"}, {"uid": "e5df7cd0fdfccd2c", "name": "测试download in play store", "time": {"start": 1755711738428, "stop": 1755711764360, "duration": 25932}, "status": "passed", "severity": "critical"}, {"uid": "63fa6ccec7fad5eb", "name": "测试disable accelerate dialogue返回正确的不支持响应", "time": {"start": 1755711304552, "stop": 1755711324987, "duration": 20435}, "status": "passed", "severity": "normal"}, {"uid": "ce3a29aa80ff9d13", "name": "测试set cover screen apps返回正确的不支持响应", "time": {"start": 1755716027137, "stop": 1755716048487, "duration": 21350}, "status": "passed", "severity": "normal"}, {"uid": "f79d0f1fb4940a1c", "name": "测试play rock music", "time": {"start": 1755701334357, "stop": 1755701366862, "duration": 32505}, "status": "passed", "severity": "critical"}, {"uid": "bab75f16bde0ccde", "name": "测试introduce yourself能正常执行", "time": {"start": 1755702786033, "stop": 1755702808456, "duration": 22423}, "status": "passed", "severity": "critical"}, {"uid": "7899813e415fcd74", "name": "测试enable zonetouch master返回正确的不支持响应", "time": {"start": 1755712263867, "stop": 1755712285139, "duration": 21272}, "status": "passed", "severity": "normal"}, {"uid": "5fd4997f0e350915", "name": "测试check status updates on whatsapp能正常执行", "time": {"start": 1755701988813, "stop": 1755702011621, "duration": 22808}, "status": "passed", "severity": "critical"}, {"uid": "5b1d5fbfac869bd8", "name": "测试play jay chou's music", "time": {"start": 1755701199251, "stop": 1755701232104, "duration": 32853}, "status": "passed", "severity": "critical"}, {"uid": "3688f63a779e5a30", "name": "测试how to say i love you in french能正常执行", "time": {"start": 1755702594293, "stop": 1755702615786, "duration": 21493}, "status": "passed", "severity": "critical"}, {"uid": "960cdd0515597ad8", "name": "测试disable unfreeze返回正确的不支持响应", "time": {"start": 1755711667964, "stop": 1755711688997, "duration": 21033}, "status": "passed", "severity": "normal"}, {"uid": "42fda6eca650eb29", "name": "测试enable touch optimization返回正确的不支持响应", "time": {"start": 1755712194449, "stop": 1755712214581, "duration": 20132}, "status": "passed", "severity": "normal"}, {"uid": "c33639a362546746", "name": "测试who is j k rowling能正常执行", "time": {"start": 1755704619903, "stop": 1755704641600, "duration": 21697}, "status": "passed", "severity": "critical"}, {"uid": "d5ec8af845715ba7", "name": "测试turn off the 8 am alarm", "time": {"start": 1755701685197, "stop": 1755701705762, "duration": 20565}, "status": "passed", "severity": "critical"}, {"uid": "91b05fae09e97bb2", "name": "测试set screen to minimum brightness返回正确的不支持响应", "time": {"start": 1755716899062, "stop": 1755716919863, "duration": 20801}, "status": "passed", "severity": "normal"}, {"uid": "a3129dbca1ab87c5", "name": "测试set scheduled power on/off and restart返回正确的不支持响应", "time": {"start": 1755716755460, "stop": 1755716776279, "duration": 20819}, "status": "passed", "severity": "normal"}, {"uid": "c23bf9ba92d12972", "name": "测试Switch to Hyper Charge能正常执行", "time": {"start": 1755708092009, "stop": 1755708112333, "duration": 20324}, "status": "failed", "severity": "critical"}, {"uid": "8b9194a09068301f", "name": "测试continue playing能正常执行", "time": {"start": 1755702062873, "stop": 1755702085196, "duration": 22323}, "status": "passed", "severity": "critical"}, {"uid": "3400b192c234228", "name": "测试Navigate to the address on the screen", "time": {"start": 1755714365612, "stop": 1755714391956, "duration": 26344}, "status": "passed", "severity": "critical"}, {"uid": "d358252686d0a9f1", "name": "测试open contact命令", "time": {"start": 1755700954911, "stop": 1755700988454, "duration": 33543}, "status": "passed", "severity": "critical"}, {"uid": "69e17ba55154faec", "name": "测试disable magic voice changer返回正确的不支持响应", "time": {"start": 1755711525591, "stop": 1755711546116, "duration": 20525}, "status": "passed", "severity": "normal"}, {"uid": "327dcb048cc0fe4", "name": "测试check model information返回正确的不支持响应", "time": {"start": 1755710915104, "stop": 1755710935537, "duration": 20433}, "status": "passed", "severity": "normal"}, {"uid": "6992a6d49c5017f9", "name": "测试what's your name？能正常执行", "time": {"start": 1755704469574, "stop": 1755704490346, "duration": 20772}, "status": "passed", "severity": "critical"}, {"uid": "cbd725d38ded5c9e", "name": "测试download qq能正常执行", "time": {"start": 1755709649631, "stop": 1755709675727, "duration": 26096}, "status": "passed", "severity": "critical"}, {"uid": "c41a5facdaf6e16", "name": "测试pause screen recording能正常执行", "time": {"start": 1755707639972, "stop": 1755707660359, "duration": 20387}, "status": "failed", "severity": "critical"}, {"uid": "abd7bf1438f0a7d2", "name": "测试turn off light theme能正常执行", "time": {"start": 1755708686834, "stop": 1755708707905, "duration": 21071}, "status": "passed", "severity": "critical"}, {"uid": "d8c8fcb4df01608b", "name": "测试Design a high-end jewelry ring themed around flamingo elements for e-commerce illustrations", "time": {"start": 1755711231762, "stop": 1755711255108, "duration": 23346}, "status": "failed", "severity": "critical"}, {"uid": "99484f3a117fde9b", "name": "测试open contact命令", "time": {"start": 1755700746451, "stop": 1755700783074, "duration": 36623}, "status": "passed", "severity": "critical"}, {"uid": "24e9abe1ba38f937", "name": "测试play political news", "time": {"start": 1755703430671, "stop": 1755703454457, "duration": 23786}, "status": "passed", "severity": "critical"}, {"uid": "d0df3bdd8860ce09", "name": "测试a clear glass cup", "time": {"start": 1755710237336, "stop": 1755710258435, "duration": 21099}, "status": "passed", "severity": "critical"}, {"uid": "3c5b657eb5c56f22", "name": "测试open folax能正常执行", "time": {"start": 1755700917970, "stop": 1755700940067, "duration": 22097}, "status": "passed", "severity": "critical"}, {"uid": "8d0be30952efafa5", "name": "测试play afro strut", "time": {"start": 1755701145485, "stop": 1755701184603, "duration": 39118}, "status": "passed", "severity": "critical"}, {"uid": "f44afbc0139dc9a7", "name": "测试start record能正常执行", "time": {"start": 1755707533841, "stop": 1755707583695, "duration": 49854}, "status": "passed", "severity": "critical"}, {"uid": "66ef2627b330a8b5", "name": "测试play the album", "time": {"start": 1755715195542, "stop": 1755715228148, "duration": 32606}, "status": "passed", "severity": "critical"}, {"uid": "c5528074071c3cf3", "name": "测试start screen recording能正常执行", "time": {"start": 1755707598522, "stop": 1755707625184, "duration": 26662}, "status": "passed", "severity": "critical"}, {"uid": "f76e37bafb506cca", "name": "测试close equilibrium mode返回正确的不支持响应", "time": {"start": 1755711126465, "stop": 1755711147400, "duration": 20935}, "status": "passed", "severity": "normal"}, {"uid": "d216d263e31042c2", "name": "测试gold coin rain能正常执行", "time": {"start": 1755712687722, "stop": 1755712712211, "duration": 24489}, "status": "passed", "severity": "critical"}, {"uid": "3eb7f8540804d8d9", "name": "测试what date is it能正常执行", "time": {"start": 1755718138197, "stop": 1755718158627, "duration": 20430}, "status": "passed", "severity": "critical"}, {"uid": "e33e43fb5c8a4fcf", "name": "测试how is the wheather today能正常执行", "time": {"start": 1755702437492, "stop": 1755702458309, "duration": 20817}, "status": "failed", "severity": "critical"}, {"uid": "fb2c209e944023e8", "name": "测试view recent alarms能正常执行", "time": {"start": 1755704242504, "stop": 1755704263386, "duration": 20882}, "status": "passed", "severity": "critical"}, {"uid": "2e076a866aa19b91", "name": "测试switch to power saving mode返回正确的不支持响应", "time": {"start": 1755717474882, "stop": 1755717495286, "duration": 20404}, "status": "passed", "severity": "normal"}, {"uid": "26d776b4fe794136", "name": "测试open whatsapp", "time": {"start": 1755709949839, "stop": 1755709971745, "duration": 21906}, "status": "failed", "severity": "critical"}, {"uid": "4f291caf47ab1d3b", "name": "测试send my recent photos to mom through whatsapp能正常执行", "time": {"start": 1755703692385, "stop": 1755703716666, "duration": 24281}, "status": "passed", "severity": "critical"}, {"uid": "45f1052203b29d68", "name": "测试measure blood oxygen", "time": {"start": 1755702937954, "stop": 1755702959235, "duration": 21281}, "status": "passed", "severity": "critical"}, {"uid": "6d7775be5b587cf2", "name": "open clock", "time": {"start": 1755700702631, "stop": 1755700731127, "duration": 28496}, "status": "failed", "severity": "critical"}, {"uid": "119856ab8c08f6bd", "name": "测试pause song能正常执行", "time": {"start": 1755701074036, "stop": 1755701095411, "duration": 21375}, "status": "passed", "severity": "critical"}, {"uid": "c4e2031bf63fbd80", "name": "测试go to office", "time": {"start": 1755712652462, "stop": 1755712673122, "duration": 20660}, "status": "passed", "severity": "critical"}, {"uid": "ace44e88029ee698", "name": "测试close aivana能正常执行", "time": {"start": 1755700203682, "stop": 1755700236832, "duration": 33150}, "status": "passed", "severity": "critical"}, {"uid": "f2bfad1ddd35325d", "name": "测试Add the images and text on the screen to the note", "time": {"start": 1755710130878, "stop": 1755710151458, "duration": 20580}, "status": "passed", "severity": "critical"}, {"uid": "13888369295144ee", "name": "测试min ring volume能正常执行", "time": {"start": 1755706808156, "stop": 1755706829809, "duration": 21653}, "status": "passed", "severity": "critical"}, {"uid": "4cc7766d33a1432f", "name": "测试call mom through whatsapp能正常执行", "time": {"start": 1755701870600, "stop": 1755701900365, "duration": 29765}, "status": "passed", "severity": "critical"}, {"uid": "76959a0932e6df2a", "name": "测试set battery saver settings返回正确的不支持响应", "time": {"start": 1755715874734, "stop": 1755715896190, "duration": 21456}, "status": "passed", "severity": "normal"}, {"uid": "4de5ac35d0b605d7", "name": "测试set call back with last used sim返回正确的不支持响应", "time": {"start": 1755715911170, "stop": 1755715941571, "duration": 30401}, "status": "passed", "severity": "normal"}, {"uid": "377e209c6953434d", "name": "测试I think the screen is a bit dark now. Could you please help me brighten it up?能正常执行", "time": {"start": 1755713407064, "stop": 1755713427864, "duration": 20800}, "status": "passed", "severity": "critical"}, {"uid": "159b858661233ae7", "name": "测试take a joke能正常执行", "time": {"start": 1755704016473, "stop": 1755704038350, "duration": 21877}, "status": "passed", "severity": "critical"}, {"uid": "d9e70fc4fdc0e68a", "name": "测试power off my phone能正常执行", "time": {"start": 1755715388628, "stop": 1755715388628, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "83e930097c463f5", "name": "测试global gdp trends能正常执行", "time": {"start": 1755702209601, "stop": 1755702232800, "duration": 23199}, "status": "passed", "severity": "critical"}, {"uid": "e934b9740d43200f", "name": "测试create a metting schedule at tomorrow能正常执行", "time": {"start": 1755700420879, "stop": 1755700447628, "duration": 26749}, "status": "failed", "severity": "critical"}, {"uid": "751695afb4b430bd", "name": "测试i want to hear a joke能正常执行", "time": {"start": 1755713536544, "stop": 1755713557931, "duration": 21387}, "status": "passed", "severity": "critical"}, {"uid": "795650584e329424", "name": "测试enable auto pickup返回正确的不支持响应", "time": {"start": 1755711965465, "stop": 1755711985632, "duration": 20167}, "status": "passed", "severity": "normal"}, {"uid": "427c0e6c562a36a3", "name": "测试flat illustration of a girl, background in avocado green, minimalist art, white dress, red lipstick, alluring gaze, green vintage earrings, profile view, soft lighting, muted tones, serene ambiance.", "time": {"start": 1755712370859, "stop": 1755712392060, "duration": 21201}, "status": "passed", "severity": "critical"}, {"uid": "959838ca0b62c989", "name": "测试happy new year能正常执行", "time": {"start": 1755712762458, "stop": 1755712786139, "duration": 23681}, "status": "passed", "severity": "critical"}, {"uid": "89ffd0d94da459ee", "name": "测试jump to auto rotate screen settings返回正确的不支持响应", "time": {"start": 1755713791059, "stop": 1755713811626, "duration": 20567}, "status": "passed", "severity": "normal"}, {"uid": "b47ff2b67a74943e", "name": "测试whatsapp能正常执行", "time": {"start": 1755710093623, "stop": 1755710116183, "duration": 22560}, "status": "passed", "severity": "critical"}, {"uid": "ee2ca97846776ba6", "name": "测试set my fonts返回正确的不支持响应", "time": {"start": 1755716459388, "stop": 1755716480480, "duration": 21092}, "status": "passed", "severity": "normal"}, {"uid": "1142c6b537107ee2", "name": "测试where is my car能正常执行", "time": {"start": 1755718394394, "stop": 1755718414764, "duration": 20370}, "status": "passed", "severity": "critical"}, {"uid": "c5d972e9ac233338", "name": "测试summarize content on this page能正常执行", "time": {"start": 1755703981243, "stop": 1755704001914, "duration": 20671}, "status": "passed", "severity": "critical"}, {"uid": "3725a642eca617b0", "name": "测试turn off flashlight能正常执行", "time": {"start": 1755708649746, "stop": 1755708672277, "duration": 22531}, "status": "passed", "severity": "critical"}, {"uid": "f86949e3bb15b41f", "name": "测试turn down ring volume能正常执行", "time": {"start": 1755708505469, "stop": 1755708526824, "duration": 21355}, "status": "passed", "severity": "critical"}, {"uid": "684ccf6e53e263d3", "name": "测试searching for a method of violent murder能正常执行", "time": {"start": 1755703657746, "stop": 1755703677263, "duration": 19517}, "status": "broken", "severity": "critical"}, {"uid": "ffcc91edf810c005", "name": "测试turn down notifications volume能正常执行", "time": {"start": 1755708469483, "stop": 1755708490778, "duration": 21295}, "status": "failed", "severity": "critical"}, {"uid": "1b08313dfe29e27", "name": "测试set timezone返回正确的不支持响应", "time": {"start": 1755717153273, "stop": 1755717174591, "duration": 21318}, "status": "passed", "severity": "normal"}, {"uid": "827684d8379c54c4", "name": "测试Kinkaku-ji", "time": {"start": 1755714127374, "stop": 1755714150399, "duration": 23025}, "status": "passed", "severity": "critical"}, {"uid": "80a57a7e444b9600", "name": "测试Enable Call on Hold返回正确的不支持响应", "time": {"start": 1755712036037, "stop": 1755712065049, "duration": 29012}, "status": "passed", "severity": "normal"}, {"uid": "6df2e183af43c36b", "name": "测试open contact命令 - 简洁版本", "time": {"start": 1755703079746, "stop": 1755703100097, "duration": 20351}, "status": "passed", "severity": "critical"}, {"uid": "3f9822a9ba521174", "name": "测试open maps", "time": {"start": 1755714657246, "stop": 1755714690988, "duration": 33742}, "status": "passed", "severity": "critical"}, {"uid": "aa022482314d4b36", "name": "测试open facebook能正常执行", "time": {"start": 1755709906929, "stop": 1755709935577, "duration": 28648}, "status": "passed", "severity": "critical"}, {"uid": "845234b003a4566f", "name": "测试set alarm for 10 o'clock", "time": {"start": 1755707193006, "stop": 1755707214799, "duration": 21793}, "status": "failed", "severity": "critical"}, {"uid": "3d2e13bc086ec514", "name": "测试help me generate a picture of a puppy", "time": {"start": 1755712979640, "stop": 1755712999712, "duration": 20072}, "status": "passed", "severity": "critical"}, {"uid": "143173435c555345", "name": "测试kill whatsapp能正常执行", "time": {"start": 1755714090161, "stop": 1755714112687, "duration": 22526}, "status": "passed", "severity": "critical"}, {"uid": "9e482f7900f6f786", "name": "测试set my alarm volume to 50%", "time": {"start": 1755707313355, "stop": 1755707335982, "duration": 22627}, "status": "passed", "severity": "critical"}, {"uid": "cac94fb9e31f53e8", "name": "测试stop music能正常执行", "time": {"start": 1755703875261, "stop": 1755703896293, "duration": 21032}, "status": "passed", "severity": "critical"}, {"uid": "b50ecabc6cc24973", "name": "测试turn off driving mode返回正确的不支持响应", "time": {"start": 1755717832334, "stop": 1755717852489, "duration": 20155}, "status": "passed", "severity": "normal"}, {"uid": "b9658b259c961db2", "name": "测试open notification ringtone settings返回正确的不支持响应", "time": {"start": 1755714705539, "stop": 1755714726431, "duration": 20892}, "status": "passed", "severity": "normal"}, {"uid": "ca8badb071247c64", "name": "测试power saving能正常执行", "time": {"start": 1755707027099, "stop": 1755707055915, "duration": 28816}, "status": "passed", "severity": "critical"}, {"uid": "847ab3cbd7b2ed1e", "name": "测试turn on brightness to 80能正常执行", "time": {"start": 1755708900298, "stop": 1755708921057, "duration": 20759}, "status": "passed", "severity": "critical"}, {"uid": "99ec4f33dc304231", "name": "测试decrease the volume to the minimun能正常执行", "time": {"start": 1755706055513, "stop": 1755706078043, "duration": 22530}, "status": "passed", "severity": "critical"}, {"uid": "39a8d0fd53a2c6ce", "name": "测试set smart hub返回正确的不支持响应", "time": {"start": 1755716970970, "stop": 1755716991444, "duration": 20474}, "status": "passed", "severity": "normal"}, {"uid": "f84d3f6caf47f216", "name": "测试switch charging modes能正常执行", "time": {"start": 1755707824470, "stop": 1755707844851, "duration": 20381}, "status": "failed", "severity": "critical"}, {"uid": "4f4c41d20a93d13f", "name": "测试say hello能正常执行", "time": {"start": 1755703540319, "stop": 1755703563881, "duration": 23562}, "status": "passed", "severity": "critical"}, {"uid": "8332a4b64b5294bb", "name": "测试order a burger能正常执行", "time": {"start": 1755709986074, "stop": 1755710006183, "duration": 20109}, "status": "failed", "severity": "critical"}, {"uid": "54ac4d0e60138b59", "name": "测试jump to adaptive brightness settings返回正确的不支持响应", "time": {"start": 1755713720283, "stop": 1755713741306, "duration": 21023}, "status": "passed", "severity": "normal"}, {"uid": "554df24716ad09c5", "name": "测试increase settings for special functions返回正确的不支持响应", "time": {"start": 1755713572625, "stop": 1755713593792, "duration": 21167}, "status": "passed", "severity": "normal"}, {"uid": "2c089ad2a03c802d", "name": "测试jump to ai wallpaper generator settings返回正确的不支持响应", "time": {"start": 1755713755631, "stop": 1755713776536, "duration": 20905}, "status": "passed", "severity": "normal"}, {"uid": "35dde70f35bbfe9b", "name": "测试Enable Call Rejection返回正确的不支持响应", "time": {"start": 1755712079475, "stop": 1755712107770, "duration": 28295}, "status": "passed", "severity": "normal"}, {"uid": "ba657cd04a6dab4c", "name": "测试download app能正常执行", "time": {"start": 1755709575847, "stop": 1755709598382, "duration": 22535}, "status": "passed", "severity": "critical"}, {"uid": "73d9c98de41b4a4c", "name": "测试resume music能正常执行", "time": {"start": 1755701504496, "stop": 1755701524938, "duration": 20442}, "status": "passed", "severity": "critical"}, {"uid": "cd18059c198a0709", "name": "测试disable zonetouch master返回正确的不支持响应", "time": {"start": 1755711703536, "stop": 1755711723927, "duration": 20391}, "status": "passed", "severity": "normal"}, {"uid": "265b97a2f55344c7", "name": "测试delete the 8 o'clock alarm", "time": {"start": 1755700462401, "stop": 1755700482675, "duration": 20274}, "status": "failed", "severity": "critical"}, {"uid": "a981a970f4f7d352", "name": "测试order a takeaway返回正确的不支持响应", "time": {"start": 1755714905123, "stop": 1755714925899, "duration": 20776}, "status": "failed", "severity": "normal"}, {"uid": "7a4f53635514e0ea", "name": "测试Language List", "time": {"start": 1755710165942, "stop": 1755710186411, "duration": 20469}, "status": "passed", "severity": "critical"}, {"uid": "783d1b35174d91e4", "name": "测试change man voice能正常执行", "time": {"start": 1755710686900, "stop": 1755710707420, "duration": 20520}, "status": "passed", "severity": "critical"}, {"uid": "d26e034db66f3bb1", "name": "测试yandex eats返回正确的不支持响应", "time": {"start": 1755718464076, "stop": 1755718484775, "duration": 20699}, "status": "passed", "severity": "normal"}, {"uid": "f77e3b68b75abd44", "name": "测试why is my phone not ringing on incoming calls能正常执行", "time": {"start": 1755704656348, "stop": 1755704685142, "duration": 28794}, "status": "passed", "severity": "critical"}, {"uid": "2fb30ed5d8c97efd", "name": "测试a little raccoon walks on a forest meadow, surrounded by a row of green bamboo forests, with layers of high mountains shrouded in mist in the distance", "time": {"start": 1755710415835, "stop": 1755710436723, "duration": 20888}, "status": "passed", "severity": "critical"}, {"uid": "5e14fb7d745f15a4", "name": "测试call mom", "time": {"start": 1755710521217, "stop": 1755710551323, "duration": 30106}, "status": "failed", "severity": "critical"}, {"uid": "52b1168d520f1090", "name": "测试switching charging speed能正常执行", "time": {"start": 1755717509854, "stop": 1755717530141, "duration": 20287}, "status": "passed", "severity": "critical"}, {"uid": "35be0a29733330f", "name": "测试puppy能正常执行", "time": {"start": 1755705189398, "stop": 1755705266614, "duration": 77216}, "status": "passed", "severity": "critical"}, {"uid": "450d5205bf19d570", "name": "测试turn on the alarm at 8 am", "time": {"start": 1755701720522, "stop": 1755701742753, "duration": 22231}, "status": "passed", "severity": "critical"}, {"uid": "ff1097fbee878942", "name": "测试check my balance of sim1返回正确的不支持响应", "time": {"start": 1755710950108, "stop": 1755710970835, "duration": 20727}, "status": "passed", "severity": "normal"}, {"uid": "595d2d67a593ff6f", "name": "测试tell me a joke能正常执行", "time": {"start": 1755704128497, "stop": 1755704149109, "duration": 20612}, "status": "failed", "severity": "critical"}, {"uid": "eb70cf2137fe243f", "name": "测试close airplane能正常执行", "time": {"start": 1755705865868, "stop": 1755705886037, "duration": 20169}, "status": "passed", "severity": "critical"}, {"uid": "6a9dd58982559231", "name": "测试jump to nfc settings", "time": {"start": 1755714012004, "stop": 1755714040036, "duration": 28032}, "status": "passed", "severity": "critical"}, {"uid": "9303e393f4bece4c", "name": "测试play music on visha", "time": {"start": 1755703347335, "stop": 1755703379238, "duration": 31903}, "status": "passed", "severity": "critical"}, {"uid": "d41160b7175fd6cf", "name": "测试how's the weather today?返回正确的不支持响应", "time": {"start": 1755702473412, "stop": 1755702500832, "duration": 27420}, "status": "failed", "severity": "normal"}, {"uid": "bf0190e9cd5a8476", "name": "测试decrease the brightness能正常执行", "time": {"start": 1755706019510, "stop": 1755706040717, "duration": 21207}, "status": "failed", "severity": "critical"}, {"uid": "e3ecb8cc47bce00f", "name": "测试document summary能正常执行", "time": {"start": 1755704955804, "stop": 1755705043062, "duration": 87258}, "status": "failed", "severity": "critical"}, {"uid": "68ed7aab547d2d9d", "name": "测试measure heart rate", "time": {"start": 1755702973812, "stop": 1755702994422, "duration": 20610}, "status": "passed", "severity": "critical"}, {"uid": "4d31fe080feb4515", "name": "测试download whatsapp能正常执行", "time": {"start": 1755711820163, "stop": 1755711845606, "duration": 25443}, "status": "passed", "severity": "critical"}, {"uid": "816c6a8b9c388749", "name": "测试open whatsapp", "time": {"start": 1755714833356, "stop": 1755714855317, "duration": 21961}, "status": "failed", "severity": "critical"}, {"uid": "51c00d45fa6af6db", "name": "测试who is harry potter能正常执行", "time": {"start": 1755704584240, "stop": 1755704605401, "duration": 21161}, "status": "passed", "severity": "critical"}, {"uid": "f9845e8245082057", "name": "测试max notifications volume能正常执行", "time": {"start": 1755706527526, "stop": 1755706549367, "duration": 21841}, "status": "passed", "severity": "critical"}, {"uid": "3b0f87ed2ba47792", "name": "测试help me generate a picture of a bamboo forest stream", "time": {"start": 1755712944825, "stop": 1755712965147, "duration": 20322}, "status": "passed", "severity": "critical"}, {"uid": "df6d3dd2ff5385ab", "name": "测试hello hello能正常执行", "time": {"start": 1755712800643, "stop": 1755712824978, "duration": 24335}, "status": "passed", "severity": "critical"}, {"uid": "54fb5a2d17f75684", "name": "测试end exercising能正常执行", "time": {"start": 1755712299813, "stop": 1755712321431, "duration": 21618}, "status": "passed", "severity": "critical"}, {"uid": "64e2ef5736676f9d", "name": "测试take notes能正常执行", "time": {"start": 1755717544335, "stop": 1755717569297, "duration": 24962}, "status": "failed", "severity": "critical"}, {"uid": "167590b8047e5e71", "name": "测试navigation to the address in thie image能正常执行", "time": {"start": 1755714406421, "stop": 1755714432815, "duration": 26394}, "status": "failed", "severity": "critical"}, {"uid": "8d93d331a494628b", "name": "测试how to say hello in french能正常执行", "time": {"start": 1755702557725, "stop": 1755702579666, "duration": 21941}, "status": "passed", "severity": "critical"}, {"uid": "a6a2d00c2b278d2", "name": "测试hello hello能正常执行", "time": {"start": 1755702283509, "stop": 1755702305954, "duration": 22445}, "status": "passed", "severity": "critical"}, {"uid": "f2c1e45e9870557e", "name": "测试remove the people from the image", "time": {"start": 1755715546687, "stop": 1755715567418, "duration": 20731}, "status": "passed", "severity": "critical"}, {"uid": "f4c408a5aabd00f2", "name": "测试Change the style of this image to 3D cartoon能正常执行", "time": {"start": 1755704829532, "stop": 1755704941072, "duration": 111540}, "status": "passed", "severity": "critical"}, {"uid": "2f8312ea7202df42", "name": "测试find a restaurant near me能正常执行", "time": {"start": 1755709690853, "stop": 1755709721504, "duration": 30651}, "status": "passed", "severity": "critical"}, {"uid": "58903762c7912ce0", "name": "测试play music on boomplayer", "time": {"start": 1755703309148, "stop": 1755703332570, "duration": 23422}, "status": "failed", "severity": "critical"}, {"uid": "a415f76a255b6a92", "name": "测试help me generate a picture of an airplane", "time": {"start": 1755713050221, "stop": 1755713070693, "duration": 20472}, "status": "passed", "severity": "critical"}, {"uid": "29d552d1101b873b", "name": "测试help me write an thanks email能正常执行", "time": {"start": 1755713262846, "stop": 1755713285052, "duration": 22206}, "status": "failed", "severity": "critical"}, {"uid": "9ca4f25a9ca3a301", "name": "测试reboot my phone能正常执行", "time": {"start": 1755715461198, "stop": 1755715461198, "duration": 0}, "status": "skipped", "severity": "critical"}, {"uid": "de96da566e06249f", "name": "测试what time is it in china能正常执行", "time": {"start": 1755718320894, "stop": 1755718343451, "duration": 22557}, "status": "passed", "severity": "critical"}, {"uid": "82733299fa7cd70e", "name": "测试where is the carlcare service outlet能正常执行", "time": {"start": 1755709534776, "stop": 1755709560729, "duration": 25953}, "status": "passed", "severity": "critical"}, {"uid": "bd37238f23d42cc1", "name": "测试set screen relay返回正确的不支持响应", "time": {"start": 1755716827173, "stop": 1755716848310, "duration": 21137}, "status": "passed", "severity": "normal"}, {"uid": "a258242e9c32e9fb", "name": "测试play love sotry", "time": {"start": 1755715059150, "stop": 1755715097376, "duration": 38226}, "status": "passed", "severity": "critical"}, {"uid": "85e50e395982bf38", "name": "测试make a call能正常执行", "time": {"start": 1755702894282, "stop": 1755702923163, "duration": 28881}, "status": "passed", "severity": "critical"}, {"uid": "8bce5d4f21a14189", "name": "测试how is the weather today能正常执行", "time": {"start": 1755702396253, "stop": 1755702422756, "duration": 26503}, "status": "failed", "severity": "critical"}, {"uid": "2b7fde4df860431e", "name": "测试set smart panel返回正确的不支持响应", "time": {"start": 1755717005889, "stop": 1755717026585, "duration": 20696}, "status": "passed", "severity": "normal"}, {"uid": "9cda2cd83df5f4d2", "name": "测试show my all alarms能正常执行", "time": {"start": 1755703768001, "stop": 1755703789007, "duration": 21006}, "status": "passed", "severity": "critical"}, {"uid": "34d823de203fd775", "name": "测试turn on auto rotate screen能正常执行", "time": {"start": 1755708829957, "stop": 1755708850102, "duration": 20145}, "status": "failed", "severity": "critical"}, {"uid": "2cadbc215e77f7e9", "name": "测试check my to-do list能正常执行", "time": {"start": 1755710985078, "stop": 1755711006198, "duration": 21120}, "status": "passed", "severity": "critical"}, {"uid": "64169ad1b4b90399", "name": "测试turn up notifications volume能正常执行", "time": {"start": 1755709355890, "stop": 1755709376884, "duration": 20994}, "status": "passed", "severity": "critical"}, {"uid": "2e2cd6de3dca64ae", "name": "测试jump to battery and power saving返回正确的不支持响应", "time": {"start": 1755713825711, "stop": 1755713847128, "duration": 21417}, "status": "passed", "severity": "normal"}, {"uid": "886ff3ab35ea4e2d", "name": "测试it wears a yellow leather collar", "time": {"start": 1755713684217, "stop": 1755713705584, "duration": 21367}, "status": "passed", "severity": "critical"}, {"uid": "96fb5e3e2559766c", "name": "测试turn off adaptive brightness能正常执行", "time": {"start": 1755708579016, "stop": 1755708599998, "duration": 20982}, "status": "passed", "severity": "critical"}, {"uid": "c4b327dbe29a37ce", "name": "测试smart charge能正常执行", "time": {"start": 1755707497944, "stop": 1755707518821, "duration": 20877}, "status": "failed", "severity": "critical"}, {"uid": "5f8ee9e3f5d79dcb", "name": "测试check contacts能正常执行", "time": {"start": 1755710835886, "stop": 1755710864916, "duration": 29030}, "status": "passed", "severity": "critical"}, {"uid": "b94f6474b6238d1a", "name": "测试disable touch optimization返回正确的不支持响应", "time": {"start": 1755711632893, "stop": 1755711653385, "duration": 20492}, "status": "passed", "severity": "normal"}]