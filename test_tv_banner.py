#!/usr/bin/env python3
"""
测试 tv_banner 和 tv_text 文本获取功能
"""

from pages.apps.ella.ella_response_handler import <PERSON>ResponseHandler
from core.base_driver import driver_manager
from core.logger import log


def test_tv_elements_text():
    """测试获取 tv_banner 和 tv_text 文本"""
    try:
        # 初始化响应处理器
        handler = EllaResponseHandler(driver_manager.driver)

        log.info("开始测试 tv_banner 和 tv_text 文本获取...")

        # 测试单独获取 tv_banner 文本
        tv_banner_text = handler.get_response_from_tv_banner()
        log.info(f"tv_banner 文本: '{tv_banner_text}'")

        # 测试单独获取 tv_text 文本
        tv_text_text = handler.get_response_from_tv_text()
        log.info(f"tv_text 文本: '{tv_text_text}'")

        # 测试获取所有文本（包含 tv_banner 和 tv_text）
        all_texts = handler.get_response_all_text()
        log.info(f"所有文本列表: {all_texts}")

        # 检查 tv_banner 是否在所有文本中
        if tv_banner_text and tv_banner_text in all_texts:
            log.info("✅ tv_banner 文本已成功包含在所有文本列表中")
        elif tv_banner_text:
            log.warning("⚠️ tv_banner 文本存在但未包含在所有文本列表中")
        else:
            log.info("ℹ️ tv_banner 文本为空（可能该元素不存在或无文本）")

        # 检查 tv_text 是否在所有文本中
        if tv_text_text and tv_text_text in all_texts:
            log.info("✅ tv_text 文本已成功包含在所有文本列表中")
        elif tv_text_text:
            log.warning("⚠️ tv_text 文本存在但未包含在所有文本列表中")
        else:
            log.info("ℹ️ tv_text 文本为空（可能该元素不存在或无文本）")

        return {
            'tv_banner': tv_banner_text,
            'tv_text': tv_text_text,
            'all_texts': all_texts
        }

    except Exception as e:
        log.error(f"测试文本获取失败: {e}")
        return {}


if __name__ == "__main__":
    test_tv_elements_text()
