"""
Ella语音助手第三方集成指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("第三方集成")
class TestEllaTurnScreenRecord(SimpleEllaTest):
    """Ella turn on the screen record 测试类"""

    @allure.title(f"stop  screen recording能正常执行")
    @allure.description(f"stop screen recording")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_end_screen_recording(self, ella_app):
        command = "end screen recording"
        expected_text = ['Screen recording finished']
        f"""{command}"""

        with allure.step(f"执行命令: turn on the screen record"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, 'turn on the screen record', verify_status=False, verify_files=True  # 第三方集成通常不验证状态
            )

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command, verify_status=False, verify_files=True  # 第三方集成通常不验证状态
            )

        with allure.step("验证响应包含期望内容"):
            result = self.verify_expected_in_response(expected_text, response_text)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step(f"验证已关闭"):
            assert not final_status, f"初始={initial_status}, 最终={final_status}, 响应='{response_text}'"

        with allure.step(f"验证文件存在"):
            assert files_status, f"文件不存在！"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")