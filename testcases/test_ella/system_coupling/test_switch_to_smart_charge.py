"""
Ella语音助手基础指令
"""
import pytest
import allure
from testcases.test_ella.base_ella_test import SimpleEllaTest


@allure.feature("Ella语音助手")
@allure.story("Ella语音助手基础指令")
class TestEllaSwitchToSmartCharge(SimpleEllaTest):
    """Ella命令测试类"""
    command = "switch to smart charge"
    expected_text = ["The current charging device does not support switching charging modes",'set default charging mode']

    @allure.title(f"测试{command}能正常执行")
    @allure.description(f"{command}")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_switch_to_smart_charge(self, ella_app):
        f"""{self.command}"""

        command = self.command

        with allure.step(f"执行命令: {command}"):
            initial_status, final_status, response_text, files_status = self.simple_command_test(
                ella_app, command
            )

        with allure.step("验证响应包含在期望中"):
            expected_text = self.expected_text
            result = self.verify_expected_in_response_advanced(expected_text, response_text,match_any=True)
            assert result, f"响应文本应包含{expected_text}，实际响应: '{response_text}'"

        with allure.step("记录测试结果"):
            summary = self.create_test_summary(command, initial_status, final_status, response_text)
            # 添加额外的验证信息
            self.attach_test_summary(summary)
            self.take_screenshot(ella_app, "test_completed")

        # pytest测试函数不应该返回值，所有验证都应该通过assert完成
